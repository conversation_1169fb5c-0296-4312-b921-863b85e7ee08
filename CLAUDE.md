# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is an RPA (Robotic Process Automation) warning system for monitoring data processing tasks across multiple brands and platforms. It consists of a Flask backend API and a React frontend built with Umi Max framework.

## Architecture

### Backend (Python Flask)
- **Location**: `backend/` 
- **Main files**: 
  - `app.py` - Flask application with monitoring APIs
  - `database.py` - Database connection utilities
  - `requirements.txt` - Python dependencies (Flask, Flask-Cors, PyMySQL)
- **Database**: Uses MySQL/PyMySQL for data storage in `rpa_data.db`
- **Key functionality**:
  - Data status matrix transformation (`transform_to_matrix`)
  - Multi-status monitoring (0=red/no data, 1=yellow/partial, 2=green/complete, 3=blue/data mismatch)
  - Brand trend analysis via `/api/brand_trend` endpoint
  - RPA rerun functionality with webhook integration for different business types

### Frontend (React + Umi Max)
- **Location**: `frontend/`
- **Framework**: Um<PERSON> (React-based framework)
- **UI Library**: Ant Design Pro Components
- **Main pages** (in `frontend/src/pages/`):
  - Dashboard - Main monitoring interface
  - RTBMonitor - RTB monitoring with data status matrix
  - RpaOperations - RPA operation controls
  - BillingMonitor, SupplyMonitor - Additional monitoring views
- **Key components**:
  - `BrandTrendPopover` - Hover popover showing brand data trends
  - Data status matrix with color-coded statuses
  - Interactive charts using trend data

## Development Commands

### Frontend Development
```bash
cd frontend
npm install          # Install dependencies
npm run dev          # Start development server (default port 8000)
npm run build        # Build for development
npm run build:prod   # Build for production (UMI_ENV=prod)
npm run format       # Format code with Prettier
npm run setup        # Setup Umi Max environment
npm start            # Alias for npm run dev
```

### Backend Development  
```bash
cd backend
pip install -r requirements.txt  # Install Python dependencies
python app.py                    # Start Flask development server (port 5001)
```

### Code Quality
```bash
cd frontend
npm run format       # Format with Prettier + organize imports
git add . && git commit -m "..."  # Triggers husky + lint-staged
```

## Key Features Implemented

### Data Status Matrix
- 4-state color coding system for monitoring data completeness:
  - 🔴 Red (0): No data in both DB and DW
  - 🟡 Yellow (1): Partial data (only DB or DW has data)
  - 🟢 Green (2): Complete data (both have data, counts match)
  - 🔵 Blue (3): Data mismatch (both have data but counts differ)
- Supports multiple brands and platforms simultaneously
- Real-time status updates with hover interactions
- Click status icons to view detailed data counts and trigger RPA rerun

### Brand Trend Analysis
- `/api/brand_trend` endpoint provides time-series data
- Default 15-day lookback period (T-1 backwards)
- Interactive popover charts showing daily data volumes
- Hover over brand names in data matrix to view trend charts
- Pure CSS+SVG implementation (no external chart dependencies)
- Auto-fills missing dates with zero values for complete timeline

### RPA Rerun Functionality
- Business-type specific webhook URLs:
  - 综合投放: `https://api.yingdao.com/api/tool/ipaas/webhook/callback/853591947531247616`
  - 投放广告: `https://api.yingdao.com/api/tool/ipaas/webhook/callback/856829098951479296`
- Automatic parameter validation and data preparation

## Database Schema

The system uses SQLite database (`rpa_data.db`) with these key fields:
- `brand`, `platform` - Entity identifiers for data sources
- `date` - Time dimension (YYYY-MM-DD format)
- `db_exists`, `dw_exists` - Boolean flags for data availability  
- `db_data_count`, `dw_data_count` - Integer counts for comparison and status calculation
- `biz_name_lvl2` - Business type classification ("综合投放", "投放广告")
- `data_table_name` - Source table identifier

### Status Calculation Logic
Status is calculated from db_exists, dw_exists, and count fields:
- Both counts > 0 and equal → Green (2)
- Both counts > 0 but different → Blue (3) 
- Only one has data → Yellow (1)
- Neither has data → Red (0)

## Configuration Files

- `frontend/package.json` - Node.js dependencies and scripts
- `frontend/tsconfig.json` - TypeScript configuration (extends Umi's config)
- `backend/requirements.txt` - Python dependencies (Flask, Flask-Cors, PyMySQL)
- Code formatting: Prettier with organize-imports plugin
- Git hooks: Husky + lint-staged for pre-commit formatting

## Project Structure Notes

### Key Components
- `BrandTrendPopover` - Reusable component for data trend visualization
- `transform_to_matrix()` - Core backend function for status matrix generation
- Main monitoring pages: Dashboard, RTBMonitor, BillingMonitor, SupplyMonitor, RpaOperations

### Development Patterns
- Frontend uses Ant Design Pro Components for consistent UI
- Backend uses Flask with CORS enabled for API endpoints
- Database connections handled through `database.py` utility module
- All charts implemented with pure CSS+SVG (no external dependencies)

### Testing
- Test files are in project root with `test_*.html` naming convention
- Backend can be tested with curl commands (see feature documentation)
- Frontend uses Umi Max's built-in dev server with hot reload

## API Endpoints

Key backend endpoints:
- `/api/brand_trend` - GET brand trend data with date range filtering
- `/api/rerun_rpa` - POST RPA rerun requests with business type routing
- Data matrix endpoints for status monitoring (various brand/platform/date combinations)

### Important API Details
- Backend runs on port 5001 by default
- All endpoints support CORS for frontend integration
- RPA rerun endpoints include 30-second timeout and comprehensive error handling
- Brand trend API auto-calculates T-1 to T-15 date range when dates not specified
# RTB监控页面品牌悬浮弹窗功能

## 功能概述

在RTB监控页面的数据状态矩阵部分，为品牌列添加了鼠标悬浮弹窗功能，当用户将鼠标悬浮在品牌名称上时，会显示该品牌在所选数据范围内每天的数据量走势折线图。

## 实现的功能

### 1. 后端API接口
- **接口路径**: `/api/brand_trend`
- **请求方法**: GET
- **参数**:
  - `brand`: 品牌名称（必填，格式：品牌名(平台名)）
  - `platform`: 平台名称（可选）
  - `start_date`: 开始日期（可选，格式：YYYY-MM-DD）
  - `end_date`: 结束日期（可选，格式：YYYY-MM-DD）
  - `biz_name_lvl2`: 二级业务名称（可选）

- **默认日期范围**: 当未指定start_date或end_date时，自动使用T-1日（昨天）往前推15天的数据

- **返回数据格式**:
```json
{
  "code": 0,
  "msg": "success",
  "data": {
    "brand": "品牌名(平台名)",
    "dates": ["2025-05-01", "2025-05-02", ...],
    "db_counts": [12000, 15000, ...],
    "dw_counts": [11500, 14800, ...]
  }
}
```

### 2. 前端组件

#### BrandTrendPopover组件
- **位置**: `frontend/src/components/BrandTrendPopover/`
- **功能**: 
  - 鼠标悬浮触发数据加载
  - 使用ECharts绘制折线图
  - 支持加载状态、错误处理和重试
  - 自动格式化数值显示（万为单位）

#### 组件特性
- **悬浮触发**: 鼠标悬浮0.3秒后显示弹窗
- **延迟隐藏**: 鼠标离开0.1秒后隐藏弹窗
- **图表样式**: 420px × 320px，优化布局避免溢出
- **数据展示**:
  - 蓝色线条：数据库数据量
  - 绿色线条：数仓数据量
  - 支持数值格式化（超过1万显示为"w"单位）
- **交互功能**:
  - 鼠标悬浮数据点显示具体数值
  - 智能横轴标签显示（避免拥挤）
  - 纵轴标签正确定位（不溢出）
- **数据处理**:
  - 自动填充缺失日期，数据量按0处理
  - 0值数据点用特殊样式显示（空心圆）
  - 保证图表时间轴的连续性和完整性

### 3. 页面集成

在RTB监控页面（`frontend/src/pages/RTBMonitor/index.tsx`）中：
- 导入BrandTrendPopover组件
- 将品牌名称包装在悬浮弹窗组件中
- 传递当前的筛选条件（日期范围、二级业务等）
- **页面默认日期范围**: 页面加载时自动选择T-1日往前推15天作为默认查询范围

## 技术实现

### 依赖库
- **无外部依赖** - 使用纯CSS+SVG实现图表，避免了Module Federation环境下的依赖加载问题

### 关键技术点

1. **数据聚合**: 后端按日期分组聚合同一品牌的数据量
2. **智能日期范围**: 未指定日期时自动使用T-1日往前推15天
3. **日期补全**: 自动生成完整日期范围，缺失数据用0填充
4. **异步加载**: 前端异步获取趋势数据，避免阻塞页面
5. **错误处理**: 完善的错误处理和重试机制
6. **性能优化**: 只在悬浮时才加载数据，避免不必要的请求
7. **用户体验**: 平滑的悬浮交互和加载状态提示
8. **数据可视化**: 0值数据点特殊样式，保持图表美观性

## 使用方法

1. 打开RTB监控页面（自动选择最近15天作为默认查询范围）
2. 可以手动调整日期范围筛选器，或使用默认的T-1日往前推15天
3. 在数据状态矩阵中，将鼠标悬浮在任意品牌名称上
4. 等待0.3秒后会显示该品牌的数据量走势图
5. 图表显示该品牌在当前筛选日期范围内的每日数据量变化
6. 鼠标移开后弹窗自动隐藏

## 测试

创建了测试页面 `test_brand_trend.html` 用于验证功能：
- 使用模拟数据演示图表效果
- 验证悬浮交互逻辑
- 测试图表渲染和样式

## 文件清单

### 新增文件
- `backend/app.py` - 添加了 `/api/brand_trend` 接口
- `frontend/src/components/BrandTrendPopover/index.tsx` - 悬浮弹窗组件
- `frontend/src/components/BrandTrendPopover/SimpleChart.tsx` - 纯CSS+SVG图表组件
- `frontend/src/components/BrandTrendPopover/index.less` - 组件样式
- `test_brand_trend.html` - 功能测试页面

### 修改文件
- `frontend/src/pages/RTBMonitor/index.tsx` - 集成悬浮弹窗组件
- `frontend/src/services/monitor/index.ts` - 添加趋势数据接口类型定义

## 注意事项

1. 确保后端服务正常运行（端口5001）
2. 无需安装额外依赖包，使用纯CSS+SVG实现图表
3. 品牌名称格式需要包含平台信息：`品牌名(平台名)`
4. 图表数据基于当前页面的筛选条件（日期范围、二级业务等）
5. 已解决Module Federation环境下的依赖加载问题

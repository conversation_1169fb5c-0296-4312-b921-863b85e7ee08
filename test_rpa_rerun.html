<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RPA重跑功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            line-height: 1.6;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .success {
            color: #52c41a;
            font-weight: bold;
        }
        .error {
            color: #ff4d4f;
            font-weight: bold;
        }
        .info {
            color: #1890ff;
        }
        button {
            background: #1890ff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #40a9ff;
        }
        button:disabled {
            background: #d9d9d9;
            cursor: not-allowed;
        }
        .result {
            background: #f0f0f0;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
        .webhook-info {
            background: #e6f7ff;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <h1>RPA重跑功能测试</h1>
    
    <div class="webhook-info">
        <strong>📋 Webhook映射关系:</strong><br>
        • 综合投放 → https://api.yingdao.com/api/tool/ipaas/webhook/callback/853591947531247616<br>
        • 投放广告 → https://api.yingdao.com/api/tool/ipaas/webhook/callback/856829098951479296
    </div>
    
    <div class="test-section">
        <h3>🧪 测试用例</h3>
        
        <h4>1. 综合投放业务</h4>
        <button onclick="testZongheToufang()">测试综合投放RPA重跑</button>
        <div id="result1" class="result"></div>
        
        <h4>2. 投放广告业务</h4>
        <button onclick="testToufangGuanggao()">测试投放广告RPA重跑</button>
        <div id="result2" class="result"></div>
        
        <h4>3. 不支持的业务类型</h4>
        <button onclick="testUnsupportedBiz()">测试不支持的业务类型</button>
        <div id="result3" class="result"></div>
        
        <h4>4. 参数缺失测试</h4>
        <button onclick="testMissingParams()">测试缺失参数</button>
        <div id="result4" class="result"></div>
    </div>

    <script>
        async function callRpaRerun(data, resultElementId) {
            const resultElement = document.getElementById(resultElementId);
            resultElement.innerHTML = '🔄 正在调用RPA重跑接口...';
            
            try {
                const response = await fetch('http://127.0.0.1:5006/api/rerun_rpa', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(data)
                });
                
                const result = await response.json();
                
                let output = `📤 请求数据:\n${JSON.stringify(data, null, 2)}\n\n`;
                output += `📥 响应结果:\n${JSON.stringify(result, null, 2)}\n\n`;
                
                if (result.code === 0) {
                    output += `✅ 状态: 成功\n`;
                    output += `📝 消息: ${result.msg}\n`;
                    if (result.webhook_response) {
                        output += `🔗 Webhook响应: 已收到外部系统响应\n`;
                    }
                } else {
                    output += `❌ 状态: 失败\n`;
                    output += `📝 错误: ${result.msg}\n`;
                }
                
                resultElement.innerHTML = output;
                
            } catch (error) {
                resultElement.innerHTML = `❌ 网络错误: ${error.message}`;
            }
        }
        
        function testZongheToufang() {
            const data = {
                brand: "三只松鼠",
                platform: "抖音",
                date: "2025-08-25",
                data_table_name: "douyin_sanzhisongsu_20250825",
                biz_name_lvl2: "综合投放"
            };
            callRpaRerun(data, 'result1');
        }
        
        function testToufangGuanggao() {
            const data = {
                brand: "二手车",
                platform: "抖音", 
                date: "2025-08-25",
                data_table_name: "douyin_ershouche_20250825",
                biz_name_lvl2: "投放广告"
            };
            callRpaRerun(data, 'result2');
        }
        
        function testUnsupportedBiz() {
            const data = {
                brand: "测试品牌",
                platform: "测试平台",
                date: "2025-08-25", 
                data_table_name: "test_table",
                biz_name_lvl2: "小时综合投放"  // 不支持的业务类型
            };
            callRpaRerun(data, 'result3');
        }
        
        function testMissingParams() {
            const data = {
                brand: "测试品牌",
                platform: "测试平台"
                // 缺少必要参数
            };
            callRpaRerun(data, 'result4');
        }
        
        // 页面加载完成后显示说明
        window.onload = function() {
            console.log('RPA重跑功能测试页面已加载');
        };
    </script>
</body>
</html>

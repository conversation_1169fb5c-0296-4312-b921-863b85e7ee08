#!/usr/bin/env python3
import requests
import json

def debug_api_difference():
    """通过API调试数据差异"""
    
    base_url = "http://127.0.0.1:5001"
    
    print("=== 通过API调试数据差异 ===")
    print()
    
    # 1. 查询monitor API的数据
    monitor_params = {
        'biz_name_lvl1': '供给数据',
        'start_date': '2025-08-29',
        'end_date': '2025-08-29',
        'platform': '美团',
        'brand': '三养'
    }
    
    print("1. Monitor API查询:")
    print(f"   参数: {monitor_params}")
    
    try:
        response = requests.get(f"{base_url}/api/monitor", params=monitor_params)
        monitor_data = response.json()
        
        if monitor_data['code'] == 0 and monitor_data['data']['matrix']:
            brand_data = monitor_data['data']['matrix'][0]
            date_data = brand_data['dates'][0]
            print(f"   结果: DB={date_data['db_data_count']}, DW={date_data['dw_data_count']}")
            print(f"   业务类型: {date_data['biz_name_lvl1']} -> {date_data['biz_name_lvl2']}")
        else:
            print(f"   错误: {monitor_data}")
    except Exception as e:
        print(f"   异常: {e}")
    
    print()
    
    # 2. 查询brand_trend API的数据
    trend_params = {
        'brand': '三养(美团)',
        'biz_name_lvl1': '供给数据',
        'biz_name_lvl2': '品牌商x城市',
        'start_date': '2025-08-29',
        'end_date': '2025-08-29'
    }
    
    print("2. Brand Trend API查询:")
    print(f"   参数: {trend_params}")
    
    try:
        response = requests.get(f"{base_url}/api/brand_trend", params=trend_params)
        trend_data = response.json()
        
        if trend_data['code'] == 0:
            db_counts = trend_data['data']['db_counts']
            dw_counts = trend_data['data']['dw_counts']
            print(f"   结果: DB={db_counts[0]}, DW={dw_counts[0]}")
        else:
            print(f"   错误: {trend_data}")
    except Exception as e:
        print(f"   异常: {e}")
    
    print()
    
    # 3. 测试不同的brand参数格式
    print("3. 测试不同brand参数格式:")
    
    brand_formats = [
        '三养(美团)',
        '三养',
        '三养（美团）'  # 中文括号
    ]
    
    for brand_format in brand_formats:
        test_params = trend_params.copy()
        test_params['brand'] = brand_format
        
        print(f"   测试brand='{brand_format}':")
        
        try:
            response = requests.get(f"{base_url}/api/brand_trend", params=test_params)
            test_data = response.json()
            
            if test_data['code'] == 0:
                db_counts = test_data['data']['db_counts']
                dw_counts = test_data['data']['dw_counts']
                print(f"     结果: DB={db_counts[0] if db_counts else 'N/A'}, DW={dw_counts[0] if dw_counts else 'N/A'}")
            else:
                print(f"     错误: {test_data.get('msg', 'Unknown error')}")
        except Exception as e:
            print(f"     异常: {e}")
    
    print()
    
    # 4. 分析差异
    print("4. 差异分析:")
    print("   Monitor API: 369条记录 (矩阵显示)")
    print("   Brand Trend API: 370条记录 (趋势图显示)")
    print("   差异: +1条记录")
    print()
    print("   可能原因:")
    print("   - brand参数格式不一致 (三养 vs 三养(美团))")
    print("   - 数据库中存在重复记录")
    print("   - API聚合逻辑不同")
    print("   - 时间范围或过滤条件差异")

if __name__ == "__main__":
    debug_api_difference()

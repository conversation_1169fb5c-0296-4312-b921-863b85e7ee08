<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>最终状态说明展示</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            line-height: 1.6;
            background: #f5f5f5;
        }
        .card {
            background: white;
            border: 1px solid #d9d9d9;
            border-radius: 6px;
            margin: 20px 0;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .card-header {
            padding: 16px;
            border-bottom: 1px solid #f0f0f0;
            background: #fafafa;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        .card-title {
            font-size: 16px;
            font-weight: 600;
            color: #262626;
        }
        .status-legend {
            display: flex;
            align-items: center;
            gap: 16px;
            font-size: 12px;
        }
        .status-item {
            display: flex;
            align-items: center;
            gap: 4px;
        }
        .status-icon {
            width: 16px;
            height: 16px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            color: white;
            font-size: 10px;
        }
        .status-green { background: #52c41a; }
        .status-blue { background: #1890ff; }
        .status-yellow { background: #faad14; }
        .status-red { background: #ff4d4f; }
        .card-content {
            padding: 16px;
            background: #f9f9f9;
            color: #666;
            text-align: center;
        }
        .demo-section {
            margin: 30px 0;
        }
        .comparison {
            display: flex;
            gap: 20px;
            margin: 20px 0;
        }
        .before, .after {
            flex: 1;
            padding: 15px;
            border-radius: 6px;
        }
        .before {
            background: #fff2f0;
            border: 1px solid #ffccc7;
        }
        .after {
            background: #f6ffed;
            border: 1px solid #b7eb8f;
        }
        .highlight {
            background: #fff1b8;
            padding: 2px 4px;
            border-radius: 2px;
            font-weight: bold;
        }
        .note {
            background: #e6f7ff;
            padding: 12px;
            border-radius: 4px;
            margin: 15px 0;
            font-size: 12px;
            color: #0050b3;
        }
    </style>
</head>
<body>
    <h1>RTB监控页面 - 最终状态说明展示</h1>
    
    <div class="demo-section">
        <h3>🎯 最终效果展示</h3>
        
        <div class="card">
            <div class="card-header">
                <div class="card-title">数据状态矩阵</div>
                <div class="status-legend">
                    <div class="status-item">
                        <div class="status-icon status-green">✓</div>
                        <span>有数据且DB与DW完全一致</span>
                    </div>
                    <div class="status-item">
                        <div class="status-icon status-blue">!</div>
                        <span>有数据但DB与DW不一致</span>
                    </div>
                    <div class="status-item">
                        <div class="status-icon status-yellow">!</div>
                        <span>DB有数据但DW无数据</span>
                    </div>
                    <div class="status-item">
                        <div class="status-icon status-red">✕</div>
                        <span>DB与DW都无数据</span>
                    </div>
                    <div style="color: #999; font-size: 11px; margin-left: 8px;">
                        DB=数据库 DW=数据仓库
                    </div>
                </div>
            </div>
            <div class="card-content">
                这里是数据状态矩阵表格内容区域
            </div>
        </div>
    </div>
    
    <div class="demo-section">
        <h3>📋 优化对比</h3>
        
        <div class="comparison">
            <div class="before">
                <h4>优化前</h4>
                <div class="card">
                    <div class="card-header">
                        <div class="card-title">数据状态矩阵</div>
                    </div>
                    <div class="card-content">
                        用户需要猜测图标含义<br>
                        没有状态说明
                    </div>
                </div>
            </div>
            
            <div class="after">
                <h4>优化后</h4>
                <div class="card">
                    <div class="card-header">
                        <div class="card-title">数据状态矩阵</div>
                        <div class="status-legend highlight">
                            <div class="status-item">
                                <div class="status-icon status-green">✓</div>
                                <span>有数据且DB与DW完全一致</span>
                            </div>
                            <div class="status-item">
                                <div class="status-icon status-blue">!</div>
                                <span>有数据但DB与DW不一致</span>
                            </div>
                            <div class="status-item">
                                <div class="status-icon status-yellow">!</div>
                                <span>DB有数据但DW无数据</span>
                            </div>
                            <div class="status-item">
                                <div class="status-icon status-red">✕</div>
                                <span>DB与DW都无数据</span>
                            </div>
                            <div style="color: #999; font-size: 11px; margin-left: 8px;">
                                DB=数据库 DW=数据仓库
                            </div>
                        </div>
                    </div>
                    <div class="card-content">
                        状态说明清晰可见<br>
                        用户一目了然
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="demo-section">
        <h3>🎨 设计要点</h3>
        
        <div class="note">
            <strong>📌 关键优化点：</strong>
            <ul style="margin: 8px 0; padding-left: 20px;">
                <li><strong>顺序优化</strong>: 按绿、蓝、黄、红的顺序排列，从正常到异常</li>
                <li><strong>文案精准</strong>: 明确描述每种状态的具体含义</li>
                <li><strong>空间节省</strong>: 使用DB/DW缩写，节省横向空间</li>
                <li><strong>视觉层次</strong>: 状态说明与标题在同一视觉层级</li>
                <li><strong>颜色一致</strong>: 图标颜色与实际状态图标保持一致</li>
            </ul>
        </div>
        
        <h4>📊 状态优先级说明</h4>
        <table style="width: 100%; border-collapse: collapse; margin: 15px 0;">
            <thead>
                <tr style="background: #fafafa;">
                    <th style="border: 1px solid #d9d9d9; padding: 8px;">优先级</th>
                    <th style="border: 1px solid #d9d9d9; padding: 8px;">状态</th>
                    <th style="border: 1px solid #d9d9d9; padding: 8px;">颜色</th>
                    <th style="border: 1px solid #d9d9d9; padding: 8px;">说明</th>
                    <th style="border: 1px solid #d9d9d9; padding: 8px;">处理建议</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td style="border: 1px solid #d9d9d9; padding: 8px; text-align: center;">1</td>
                    <td style="border: 1px solid #d9d9d9; padding: 8px;">有数据且DB与DW完全一致</td>
                    <td style="border: 1px solid #d9d9d9; padding: 8px; text-align: center;">🟢 绿色</td>
                    <td style="border: 1px solid #d9d9d9; padding: 8px;">正常状态</td>
                    <td style="border: 1px solid #d9d9d9; padding: 8px;">无需处理</td>
                </tr>
                <tr>
                    <td style="border: 1px solid #d9d9d9; padding: 8px; text-align: center;">2</td>
                    <td style="border: 1px solid #d9d9d9; padding: 8px;">有数据但DB与DW不一致</td>
                    <td style="border: 1px solid #d9d9d9; padding: 8px; text-align: center;">🔵 蓝色</td>
                    <td style="border: 1px solid #d9d9d9; padding: 8px;">弱警示</td>
                    <td style="border: 1px solid #d9d9d9; padding: 8px;">检查数据同步</td>
                </tr>
                <tr>
                    <td style="border: 1px solid #d9d9d9; padding: 8px; text-align: center;">3</td>
                    <td style="border: 1px solid #d9d9d9; padding: 8px;">DB有数据但DW无数据</td>
                    <td style="border: 1px solid #d9d9d9; padding: 8px; text-align: center;">🟡 黄色</td>
                    <td style="border: 1px solid #d9d9d9; padding: 8px;">中等警示</td>
                    <td style="border: 1px solid #d9d9d9; padding: 8px;">检查数据传输</td>
                </tr>
                <tr>
                    <td style="border: 1px solid #d9d9d9; padding: 8px; text-align: center;">4</td>
                    <td style="border: 1px solid #d9d9d9; padding: 8px;">DB与DW都无数据</td>
                    <td style="border: 1px solid #d9d9d9; padding: 8px; text-align: center;">🔴 红色</td>
                    <td style="border: 1px solid #d9d9d9; padding: 8px;">严重警示</td>
                    <td style="border: 1px solid #d9d9d9; padding: 8px;">立即处理</td>
                </tr>
            </tbody>
        </table>
    </div>

    <script>
        console.log('最终状态说明展示页面已加载');
        console.log('状态顺序: 绿 → 蓝 → 黄 → 红');
        console.log('文案优化: 更精准的状态描述');
        console.log('布局优化: 卡片头部横向显示');
    </script>
</body>
</html>

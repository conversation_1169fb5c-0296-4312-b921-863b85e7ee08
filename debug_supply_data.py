#!/usr/bin/env python3
import pymysql

def debug_supply_data():
    """调试供给数据的子类型"""
    
    # 数据库连接配置
    config = {
        'host': '127.0.0.1',
        'port': 3306,
        'user': 'root',
        'password': '123456',
        'database': 'rpa_warning',
        'charset': 'utf8mb4',
        'cursorclass': pymysql.cursors.DictCursor
    }
    
    try:
        conn = pymysql.connect(**config)
        with conn.cursor() as cursor:
            print("=== 调试供给数据的子类型 ===")
            print()
            
            # 查询三养品牌在美团平台的供给数据所有子类型
            sql1 = """
            SELECT biz_name_lvl2, COUNT(*) as count,
                   SUM(db_data_count) as total_db_count,
                   SUM(dw_data_count) as total_dw_count
            FROM rpa_data_monitor 
            WHERE brand = '三养' AND platform = '美团' 
                  AND biz_name_lvl1 = '供给数据'
                  AND date = '20250829'
            GROUP BY biz_name_lvl2
            ORDER BY biz_name_lvl2
            """
            
            cursor.execute(sql1)
            supply_subtypes = cursor.fetchall()
            
            print("1. 供给数据的所有子类型:")
            print("-" * 80)
            total_db = 0
            total_dw = 0
            for subtype in supply_subtypes:
                print(f"   {subtype['biz_name_lvl2'] or 'NULL':20s} | "
                      f"记录数:{subtype['count']:2d} | "
                      f"DB:{subtype['total_db_count']:6d} | "
                      f"DW:{subtype['total_dw_count']:6d}")
                total_db += subtype['total_db_count']
                total_dw += subtype['total_dw_count']
            
            print("-" * 80)
            print(f"   {'总计':20s} | {'':8s} | DB:{total_db:6d} | DW:{total_dw:6d}")
            print()
            
            # 查询品牌商x城市的详细数据
            sql2 = """
            SELECT brand, platform, biz_name_lvl1, biz_name_lvl2, 
                   db_data_count, dw_data_count, data_type, period_type
            FROM rpa_data_monitor 
            WHERE brand = '三养' AND platform = '美团' 
                  AND biz_name_lvl1 = '供给数据'
                  AND biz_name_lvl2 = '品牌商x城市'
                  AND date = '20250829'
            """
            
            cursor.execute(sql2)
            brand_city_records = cursor.fetchall()
            
            print("2. 品牌商x城市的详细记录:")
            print("-" * 80)
            for i, record in enumerate(brand_city_records, 1):
                print(f"   {i}. DB:{record['db_data_count']:4d} | DW:{record['dw_data_count']:4d} | "
                      f"{record['data_type'] or 'NULL':4s} | {record['period_type'] or 'NULL':2s}")
            print()
            
            # 分析问题
            print("3. 问题分析:")
            print("-" * 80)
            print(f"   矩阵显示的数据: 369 (品牌商x城市)")
            print(f"   趋势图显示的数据: {total_db} (所有供给数据子类型总和)")
            print(f"   数据差异: {total_db - 369}")
            print()
            print("   结论: 趋势图聚合了所有供给数据子类型，而矩阵只显示特定子类型")
            
    except Exception as e:
        print(f"错误: {e}")
    finally:
        try:
            conn.close()
        except:
            pass

if __name__ == "__main__":
    debug_supply_data()

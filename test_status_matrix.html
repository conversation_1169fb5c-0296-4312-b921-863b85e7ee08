<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据状态矩阵测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            line-height: 1.6;
        }
        .status-legend {
            display: flex;
            gap: 20px;
            margin: 20px 0;
            padding: 15px;
            background: #f9f9f9;
            border-radius: 8px;
        }
        .status-item {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .status-icon {
            width: 24px;
            height: 24px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            color: white;
        }
        .status-0 { background: #ff4d4f; }
        .status-1 { background: #faad14; }
        .status-2 { background: #52c41a; }
        .status-3 { background: #1890ff; }
        
        .matrix-demo {
            margin: 20px 0;
        }
        .demo-table {
            border-collapse: collapse;
            width: 100%;
            margin: 10px 0;
        }
        .demo-table th, .demo-table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: center;
        }
        .demo-table th {
            background: #fafafa;
            font-weight: 600;
        }
        .demo-table td:first-child {
            text-align: left;
            font-weight: bold;
        }
        .status-cell {
            cursor: pointer;
            transition: transform 0.2s;
        }
        .status-cell:hover {
            transform: scale(1.1);
        }
        .explanation {
            background: #e6f7ff;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .test-cases {
            background: #f6ffed;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <h1>数据状态矩阵 - 四种状态演示</h1>
    
    <div class="explanation">
        <h3>📊 状态说明</h3>
        <p>数据状态矩阵现在支持四种状态，用不同颜色的图标表示：</p>
    </div>
    
    <div class="status-legend">
        <div class="status-item">
            <div class="status-icon status-0">✕</div>
            <span><strong>状态0 - 红色</strong>: 无数据（数据库和数仓都没有数据）</span>
        </div>
        <div class="status-item">
            <div class="status-icon status-1">!</div>
            <span><strong>状态1 - 黄色</strong>: 部分数据（只有数据库或只有数仓有数据）</span>
        </div>
        <div class="status-item">
            <div class="status-icon status-2">✓</div>
            <span><strong>状态2 - 绿色</strong>: 完整数据（数据库和数仓都有数据且数量相等）</span>
        </div>
        <div class="status-item">
            <div class="status-icon status-3">!</div>
            <span><strong>状态3 - 蓝色</strong>: 数据不一致（数据库和数仓都有数据但数量不相等）</span>
        </div>
    </div>
    
    <div class="matrix-demo">
        <h3>🎯 状态矩阵演示</h3>
        <table class="demo-table">
            <thead>
                <tr>
                    <th>品牌</th>
                    <th>08/23</th>
                    <th>08/24</th>
                    <th>08/25</th>
                    <th>08/26</th>
                    <th>08/27</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>三只松鼠(抖音)</td>
                    <td class="status-cell" onclick="showDetail(2, '数据库: 15000条, 数仓: 15000条')">
                        <div class="status-icon status-2">✓</div>
                    </td>
                    <td class="status-cell" onclick="showDetail(3, '数据库: 12000条, 数仓: 11800条')">
                        <div class="status-icon status-3">!</div>
                    </td>
                    <td class="status-cell" onclick="showDetail(1, '数据库: 8000条, 数仓: 0条')">
                        <div class="status-icon status-1">!</div>
                    </td>
                    <td class="status-cell" onclick="showDetail(0, '数据库: 0条, 数仓: 0条')">
                        <div class="status-icon status-0">✕</div>
                    </td>
                    <td class="status-cell" onclick="showDetail(3, '数据库: 20000条, 数仓: 19500条')">
                        <div class="status-icon status-3">!</div>
                    </td>
                </tr>
                <tr>
                    <td>二手车(抖音)</td>
                    <td class="status-cell" onclick="showDetail(2, '数据库: 9500条, 数仓: 9500条')">
                        <div class="status-icon status-2">✓</div>
                    </td>
                    <td class="status-cell" onclick="showDetail(2, '数据库: 11000条, 数仓: 11000条')">
                        <div class="status-icon status-2">✓</div>
                    </td>
                    <td class="status-cell" onclick="showDetail(3, '数据库: 10500条, 数仓: 10200条')">
                        <div class="status-icon status-3">!</div>
                    </td>
                    <td class="status-cell" onclick="showDetail(1, '数据库: 0条, 数仓: 12000条')">
                        <div class="status-icon status-1">!</div>
                    </td>
                    <td class="status-cell" onclick="showDetail(2, '数据库: 13500条, 数仓: 13500条')">
                        <div class="status-icon status-2">✓</div>
                    </td>
                </tr>
                <tr>
                    <td>佳能(抖音)</td>
                    <td class="status-cell" onclick="showDetail(1, '数据库: 5000条, 数仓: 0条')">
                        <div class="status-icon status-1">!</div>
                    </td>
                    <td class="status-cell" onclick="showDetail(3, '数据库: 7500条, 数仓: 7200条')">
                        <div class="status-icon status-3">!</div>
                    </td>
                    <td class="status-cell" onclick="showDetail(3, '数据库: 8200条, 数仓: 8500条')">
                        <div class="status-icon status-3">!</div>
                    </td>
                    <td class="status-cell" onclick="showDetail(2, '数据库: 9000条, 数仓: 9000条')">
                        <div class="status-icon status-2">✓</div>
                    </td>
                    <td class="status-cell" onclick="showDetail(0, '数据库: 0条, 数仓: 0条')">
                        <div class="status-icon status-0">✕</div>
                    </td>
                </tr>
            </tbody>
        </table>
    </div>
    
    <div class="test-cases">
        <h3>🧪 测试用例说明</h3>
        <ul>
            <li><strong>状态0 (红色)</strong>: 数据库和数仓都没有数据，表示该日期完全没有数据采集</li>
            <li><strong>状态1 (黄色)</strong>: 只有数据库或只有数仓有数据，表示数据流程不完整</li>
            <li><strong>状态2 (绿色)</strong>: 数据库和数仓都有数据且数量相等，表示数据正常</li>
            <li><strong>状态3 (蓝色)</strong>: 数据库和数仓都有数据但数量不相等，表示数据可能有丢失或重复</li>
        </ul>
        <p><strong>💡 新增功能</strong>: 蓝色状态用于弱警示数据不一致的情况，帮助及时发现数据质量问题。</p>
    </div>
    
    <div id="detail-info" style="margin-top: 20px; padding: 10px; background: #f0f0f0; border-radius: 4px; display: none;">
        <h4>📋 详细信息</h4>
        <div id="detail-content"></div>
    </div>

    <script>
        function showDetail(status, info) {
            const statusNames = {
                0: '无数据 (红色)',
                1: '部分数据 (黄色)', 
                2: '完整数据 (绿色)',
                3: '数据不一致 (蓝色)'
            };
            
            const detailDiv = document.getElementById('detail-info');
            const contentDiv = document.getElementById('detail-content');
            
            contentDiv.innerHTML = `
                <p><strong>状态</strong>: ${statusNames[status]}</p>
                <p><strong>详情</strong>: ${info}</p>
                <p><strong>说明</strong>: ${getStatusExplanation(status)}</p>
            `;
            
            detailDiv.style.display = 'block';
        }
        
        function getStatusExplanation(status) {
            switch(status) {
                case 0: return '需要检查数据采集流程，可能存在系统故障或配置问题';
                case 1: return '数据流程不完整，需要检查数据传输或处理环节';
                case 2: return '数据状态正常，数据库和数仓数据一致';
                case 3: return '数据不一致，建议检查数据同步过程，可能存在数据丢失或重复';
                default: return '未知状态';
            }
        }
        
        // 页面加载完成提示
        window.onload = function() {
            console.log('数据状态矩阵演示页面已加载');
            console.log('点击任意状态图标查看详细信息');
        };
    </script>
</body>
</html>

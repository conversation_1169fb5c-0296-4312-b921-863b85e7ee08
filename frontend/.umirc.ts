import { defineConfig } from '@umijs/max';

export default defineConfig({
  antd: {},
  access: {},
  model: {},
  initialState: {},
  request: {},
  layout: {
    title: 'RPA预警监控系统',
  },
  proxy: {
    '/api': {
      target: 'http://127.0.0.1:5001',
      changeOrigin: true,
    },
  },
  routes: [
    {
      path: '/',
      redirect: '/full'
    },
    {
      path: '/full',
      component: './Dashboard',
      name: '全量数据监控'
    },
    {
      path: '/bill',
      component: './BillingMonitor',
      name: '活动数据监控'
    },
    {
      path: '/supply',
      component: './SupplyMonitor',
      name: '供给数据监控'
    },
    {
      path: '/customer',
      component: './CustomerMonitor',
      name: '新老客数据监控'
    },
    {
      path: '/rtb',
      component: './RTBMonitor',
      name: 'RTB数据监控'
    },
    
  ],
  npmClient: 'pnpm',
});


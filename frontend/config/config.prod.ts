import { defineConfig } from '@umijs/max';

export default defineConfig({
  antd: {},
  access: {},
  model: {},
  initialState: {},
  request: {},
  layout: {
    title: 'RPA预警监控系统',
  },
  // 生产环境不需要代理，nginx 会处理 /api 路由
  routes: [
    {
      path: '/',
      redirect: '/full'
    },
    {
      path: '/full',
      component: './Dashboard',
      name: '全量数据监控'
    },
    {
      path: '/bill',
      component: './BillingMonitor',
      name: '活动数据监控'
    },
    {
      path: '/supply',
      component: './SupplyMonitor',
      name: '供给数据监控'
    },
    {
      path: '/customer',
      component: './CustomerMonitor',
      name: '新老客数据监控'
    },
    {
      path: '/rtb',
      component: './RTBMonitor',
      name: 'RTB数据监控'
    },
    {
      path: '/industry',
      component: './IndustryMonitor',
      name: '行业数据监控'
    },
  ],
  npmClient: 'pnpm',
  // 生产环境构建配置
  define: {
    'process.env.NODE_ENV': 'production',
  },
  // 优化构建
  chunks: ['vendors', 'umi'],
  chainWebpack: function (config: any) {
    config.merge({
      optimization: {
        splitChunks: {
          chunks: 'all',
          cacheGroups: {
            vendors: {
              name: 'vendors',
              test: /[\\/]node_modules[\\/]/,
              priority: 10,
            },
          },
        },
      },
    });
  },
});

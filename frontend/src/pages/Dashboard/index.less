.matrix-table {
  .status-icon-cell {
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    padding: 4px;
    transition: all 0.3s ease;

    &:hover {
      transform: scale(1.1);
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    }
  }

  table {
    width: 100%;
    border-collapse: collapse;
    min-width: 800px;

    th, td {
      border: 1px solid #d9d9d9;
      padding: 8px;
      text-align: center;
    }

    th {
      background: #fafafa;
      font-weight: 600;
    }

    td:first-child {
      text-align: left;
      font-weight: bold;
      min-width: 150px;
    }
  }
}

.stats-cards {
  margin-bottom: 16px;

  .ant-card {
    text-align: center;
  }
}

.brand-name-hover {
  cursor: pointer;
  transition: color 0.2s ease;
  
  &:hover {
    color: #1890ff;
    text-decoration: underline;
  }
}
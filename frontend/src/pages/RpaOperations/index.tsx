import { Page<PERSON>ontainer, ProTable } from '@ant-design/pro-components';
import { Button, message, Tag } from 'antd';
import { request } from '@umijs/max';
import type { ProColumns } from '@ant-design/pro-components';

// 定义我们从API接收的数据类型
interface DataItem {
  id: number;
  date: string;
  platform: string;
  brand: string;
  biz_name_lvl1: string;
  biz_name_lvl2: string;
  db_exists: number;
  dw_exists: number;
  db_data_count: number;
  dw_data_count: number;
  data_table_name?: string;
  dw_table_name?: string;
  dw_refresh_time?: string;
}

// API调用函数
const fetchData = async (params: any) => {
  try {
    const queryParams: any = {};

    // 只传递有值的参数
    if (params.dateRange && params.dateRange[0] && params.dateRange[1]) {
      queryParams.start_date = params.dateRange[0].format('YYYY-MM-DD');
      queryParams.end_date = params.dateRange[1].format('YYYY-MM-DD');
    }
    if (params.platform && params.platform.trim()) {
      queryParams.platform = params.platform.trim();
    }
    if (params.brand && params.brand.trim()) {
      queryParams.brand = params.brand.trim();
    }
    if (params.biz_name_lvl1 && params.biz_name_lvl1.trim()) {
      queryParams.biz_name_lvl1 = params.biz_name_lvl1.trim();
    }
    if (params.biz_name_lvl2 && params.biz_name_lvl2.trim()) {
      queryParams.biz_name_lvl2 = params.biz_name_lvl2.trim();
    }

    const response = await request('/api/monitor', {
      method: 'GET',
      params: queryParams,
    });

    if (response.code !== 0) {
      throw new Error(response.msg || '接口返回错误');
    }

    return { data: response.data || [], success: true };
  } catch (error) {
    message.error('获取数据失败');
    return { data: [], success: false };
  }
};

const handleRefreshDw = async (record: DataItem) => {
  try {
    const res = await request('/api/refresh_dw', {
      method: 'POST',
      data: record
    });
    message.success(res.message);
  } catch (error) {
    message.error('操作失败');
  }
};

const handleRerunRpa = async (record: DataItem) => {
  try {
    const res = await request('/api/rerun_rpa', {
      method: 'POST',
      data: record
    });
    message.success(res.message);
  } catch (error) {
    message.error('操作失败');
  }
};

const DashboardPage = () => {
  const columns: ProColumns<DataItem>[] = [
    {
      title: '日期',
      dataIndex: 'date',
      sorter: true,
      render: (_, record) => {
        // 将 yyyymmdd 格式转换为 yyyy-mm-dd 显示
        const date = record.date;
        if (date && date.length === 8) {
          return `${date.slice(0, 4)}-${date.slice(4, 6)}-${date.slice(6, 8)}`;
        }
        return date;
      }
    },
    { title: '平台', dataIndex: 'platform', valueType: 'text' },
    { title: '品牌', dataIndex: 'brand', valueType: 'text' },
    { title: '一级业务', dataIndex: 'biz_name_lvl1', valueType: 'text' },
    { title: '二级业务', dataIndex: 'biz_name_lvl2', valueType: 'text' },
    {
      title: '数据库状态',
      dataIndex: 'db_exists',
      render: (_, record) => (
        <Tag color={record.db_exists ? 'green' : 'red'}>
          {record.db_exists ? '存在' : '不存在'}
        </Tag>
      ),
    },
    {
      title: '数仓状态',
      dataIndex: 'dw_exists',
      render: (_, record) => (
        <Tag color={record.dw_exists ? 'blue' : 'orange'}>
          {record.dw_exists ? '存在' : '未同步'}
        </Tag>
      ),
    },
    { title: 'DB数据条数', dataIndex: 'db_data_count', sorter: true },
    { title: 'DW数据条数', dataIndex: 'dw_data_count', sorter: true },
    {
      title: '异常标记',
      render: (_, record) => {
        const isAnomaly = record.db_exists && !record.dw_exists;
        return isAnomaly ? <Tag color="volcano">异常</Tag> : <Tag color="green">正常</Tag>;
      },
    },
    {
      title: '操作',
      key: 'action',
      valueType: 'option',
      render: (_, record) => {
        const actions = [];
        if (record.db_exists && !record.dw_exists) {
          actions.push(<Button key="refresh" type="primary" onClick={() => handleRefreshDw(record)}>刷新数仓</Button>);
        }
        if (!record.db_exists) {
          actions.push(<Button key="rerun" danger onClick={() => handleRerunRpa(record)}>重跑RPA</Button>);
        }
        return actions;
      },
    },
  ];

  return (
    <PageContainer title="RPA数据监控运维">
      <ProTable<DataItem>
        columns={columns}
        cardBordered
        request={fetchData}
        rowKey="id"
        search={false}
        dateFormatter="string"
        headerTitle="数据列表"
      />
    </PageContainer>
  );
};

export default DashboardPage;


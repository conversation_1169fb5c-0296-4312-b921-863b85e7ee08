import { <PERSON>Container } from '@ant-design/pro-components';
import { Card, DatePicker, Input, Button, Alert, Space, Row, Col, Statistic, Modal, Descriptions, Tag, message, Select } from 'antd';
import { CheckCircleTwoTone, CloseCircleTwoTone, ExclamationCircleTwoTone } from '@ant-design/icons';
import React, { useEffect, useState } from 'react';
import { getMonitor } from '@/services/monitor';
import BrandTrendPopover from '@/components/BrandTrendPopover';
import ExcelFreezeTable from '@/components/ExcelFreezeTable';
import dayjs from 'dayjs';
import './index.less';

const { RangePicker } = DatePicker;

// 定义数据类型
interface MatrixData {
  brands: string[];
  dates: string[];
  matrix: {
    brand: string;
    dates: {
      status: number;
      db_exists: boolean;
      dw_exists: boolean;
      db_data_count: number;
      dw_data_count: number;
      data_table_name?: string;
      dw_table_name?: string;
      dw_refresh_time?: string;
      biz_name_lvl1?: string;
      biz_name_lvl2?: string;
      raw_data?: any;
    }[];
  }[];
  stats: {
    total_brands: number;
    total_platforms: number;
    anomaly_count: number;
    today_completion_rate: string;
  };
}

const RtbMonitorPage: React.FC = () => {
  const [matrixData, setMatrixData] = useState<MatrixData | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string>('');
  const [detailModalVisible, setDetailModalVisible] = useState(false);
  const [selectedDetail, setSelectedDetail] = useState<any>(null);

  // 初始化默认日期范围：T-1日往前推15天
  const getDefaultDateRange = () => {
    const yesterday = dayjs().subtract(1, 'day');
    const startDate = yesterday.subtract(14, 'day');
    return [startDate, yesterday];
  };

  // filters
  const [dateRange, setDateRange] = useState<any>(getDefaultDateRange());
  const [platform, setPlatform] = useState<string>('');
  const [brand, setBrand] = useState<string>('');
  const [biz2, setBiz2] = useState<string>('综合投放');

  // 二级业务选项
  const biz2Options = [
    { label: '小时综合投放', value: '小时综合投放' },
    { label: '商品分析', value: '投放广告' }, // 前端显示"商品分析"，后端仍使用"投放广告"
    { label: '综合投放', value: '综合投放' }
  ];

  const fetchData = async () => {
    setLoading(true);
    setError('');
    try {
      const query: any = {};

      // RTB监控页面只显示RTB数据
      query.biz_name_lvl1 = 'RTB';

      // 只传递有值的参数
      if (platform && platform.trim()) {
        query.platform = platform.trim();
      }
      if (brand && brand.trim()) {
        query.brand = brand.trim();
      }
      // 二级业务是必填的，总是传递
      if (biz2) {
        query.biz_name_lvl2 = biz2;
      }
      if (dateRange && dateRange[0] && dateRange[1]) {
        query.start_date = dateRange[0].format('YYYY-MM-DD');
        query.end_date = dateRange[1].format('YYYY-MM-DD');
      }

      const json = await getMonitor(query);
      if (json.code !== 0) throw new Error(json.msg || '接口返回错误');
      console.log('Matrix data received:', json.data); // 调试信息
      setMatrixData(json.data);
    } catch (e: any) {
      setError(e.message || String(e));
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchData();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const onSearch = () => {
    fetchData();
  };

  // 获取状态文本
  const getStatusText = (status: number) => {
    switch (status) {
      case 0: return '无数据';
      case 1: return '部分数据';
      case 2: return '完整数据';
      case 3: return '数据不一致';
      default: return '未知';
    }
  };

  // 渲染状态图标
  const renderStatusIcon = (status: number) => {
    switch (status) {
      case 0:
        return <CloseCircleTwoTone twoToneColor="#ff4d4f" style={{ fontSize: '24px' }} />;
      case 1:
        return <ExclamationCircleTwoTone twoToneColor="#faad14" style={{ fontSize: '24px' }} />;
      case 2:
        return <CheckCircleTwoTone twoToneColor="#52c41a" style={{ fontSize: '24px' }} />;
      case 3:
        return <ExclamationCircleTwoTone twoToneColor="#1890ff" style={{ fontSize: '24px' }} />;
      default:
        return <CloseCircleTwoTone twoToneColor="#d9d9d9" style={{ fontSize: '24px' }} />;
    }
  };

  // 点击单元格显示详情
  const handleCellClick = (brandData: any, dateData: any, date: string) => {
    setSelectedDetail({
      brand: brandData.brand,
      date: date,
      ...dateData
    });
    setDetailModalVisible(true);
  };

  // 格式化日期显示
  const formatDate = (dateStr: string) => {
    if (dateStr && dateStr.length === 8) {
      return `${dateStr.slice(4, 6)}/${dateStr.slice(6, 8)}`;
    }
    return dateStr;
  };

  // RPA重跑处理函数
  const handleRerunRpa = async (record: any) => {
    try {
      const response = await fetch('/api/rerun_rpa', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(record.raw_data || record),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      if (result.code === 0) {
        message.success(result.msg || 'RPA重跑请求已提交成功！');
        setDetailModalVisible(false);
        // 刷新数据
        fetchData();
      } else {
        throw new Error(result.msg || 'RPA重跑失败');
      }
    } catch (error: any) {
      message.error(`RPA重跑失败: ${error.message}`);
    }
  };

  // 数仓刷数处理函数
  const handleRefreshDw = async (record: any) => {
    try {
      const response = await fetch('/api/refresh_dw', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(record.raw_data || record),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      if (result.code === 0) {
        message.success(result.msg || '数仓刷数请求已提交成功！');
        setDetailModalVisible(false);
        // 刷新数据
        fetchData();
      } else {
        throw new Error(result.msg || '数仓刷数失败');
      }
    } catch (error: any) {
      message.error(`数仓刷数失败: ${error.message}`);
    }
  };

  return (
    <PageContainer header={{ title: 'RTB监控' }}>
      <Card>
        {/* 筛选器 */}
        <Space wrap style={{ marginBottom: 16 }}>
          <span>日期范围：</span>
          <RangePicker value={dateRange} onChange={setDateRange} allowClear />
          <span>平台：</span>
          <Input placeholder="平台" value={platform} onChange={(e) => setPlatform(e.target.value)} style={{ width: 160 }} />
          <span>品牌：</span>
          <Input placeholder="品牌" value={brand} onChange={(e) => setBrand(e.target.value)} style={{ width: 160 }} />
          <span>二级业务：</span>
          <Select
            placeholder="请选择二级业务"
            value={biz2}
            onChange={setBiz2}
            options={biz2Options}
            style={{ width: 160 }}
          />
          <Button type="primary" onClick={onSearch}>查询</Button>
        </Space>

        {/* 默认日期范围提示 */}
        {dateRange && dateRange[0] && dateRange[1] && (
          <div style={{ marginBottom: 12, fontSize: 12, color: '#666' }}>
            当前查询范围：{dateRange[0].format('YYYY-MM-DD')} 至 {dateRange[1].format('YYYY-MM-DD')}
            {dateRange[0].isSame(dayjs().subtract(15, 'day'), 'day') && dateRange[1].isSame(dayjs().subtract(1, 'day'), 'day') &&
              <span style={{ color: '#52c41a' }}> (默认最近15天)</span>
            }
          </div>
        )}

        {error && <Alert type="error" message={`加载失败：${error}`} style={{ marginBottom: 12 }} />}

        {/* 统计信息 */}
        {matrixData && (
          <Row gutter={16} className="stats-cards">
            <Col span={6}>
              <Card>
                <Statistic title="品牌数量" value={matrixData.stats.total_brands} />
              </Card>
            </Col>
            <Col span={6}>
              <Card>
                <Statistic title="覆盖平台" value={matrixData.stats.total_platforms} />
              </Card>
            </Col>
            <Col span={6}>
              <Card>
                <Statistic title="异常数量" value={matrixData.stats.anomaly_count} />
              </Card>
            </Col>
            <Col span={6}>
              <Card>
                <Statistic title="任务成功率" value={matrixData.stats.today_completion_rate} />
              </Card>
            </Col>
          </Row>
        )}

        {/* 矩阵表格 */}
        {matrixData && (
          <Card
            title={
              <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <span>数据状态矩阵 <span style={{ fontSize: '12px', color: '#999', fontWeight: 'normal' }}>(每10分钟刷新近15天数据状态)</span></span>
                <div style={{ display: 'flex', alignItems: 'center', gap: 16, fontSize: 12 }}>
                  <div style={{ display: 'flex', alignItems: 'center', gap: 4 }}>
                    <CheckCircleTwoTone twoToneColor="#52c41a" style={{ fontSize: '16px' }} />
                    <span>有数据且DB与DW完全一致</span>
                  </div>
                  <div style={{ display: 'flex', alignItems: 'center', gap: 4 }}>
                    <ExclamationCircleTwoTone twoToneColor="#1890ff" style={{ fontSize: '16px' }} />
                    <span>有数据但DB与DW不一致</span>
                  </div>
                  <div style={{ display: 'flex', alignItems: 'center', gap: 4 }}>
                    <ExclamationCircleTwoTone twoToneColor="#faad14" style={{ fontSize: '16px' }} />
                    <span>DB有数据但DW无数据</span>
                  </div>
                  <div style={{ display: 'flex', alignItems: 'center', gap: 4 }}>
                    <CloseCircleTwoTone twoToneColor="#ff4d4f" style={{ fontSize: '16px' }} />
                    <span>DB与DW都无数据</span>
                  </div>
                  <div style={{ color: '#999', fontSize: 11, marginLeft: 8 }}>
                    DB=数据库 DW=数据仓库
                  </div>
                </div>
              </div>
            }
            loading={loading}
          >
            <ExcelFreezeTable
              headers={matrixData.dates}
              rows={matrixData.matrix.map((brandData: any) => ({
                brand: brandData.brand,
                cells: brandData.dates.map((dateData: any, dateIndex: number) => (
                  <div
                    className="status-icon-cell"
                    onClick={() => handleCellClick(brandData, dateData, matrixData.dates[dateIndex])}
                    title={getStatusText(dateData.status)}
                  >
                    {renderStatusIcon(dateData.status)}
                  </div>
                ))
              }))}
              formatHeader={formatDate}
              extractBrandName={(brand: string) => brand}
              renderBrandCell={(brand: string) => (
                <BrandTrendPopover
                  brand={brand}
                  dateRange={dateRange ? [dateRange[0].format('YYYY-MM-DD'), dateRange[1].format('YYYY-MM-DD')] : undefined}
                  biz2={biz2}
                >
                  <span className="brand-name-hover">{brand}</span>
                </BrandTrendPopover>
              )}
              className="matrix-table"
            />
          </Card>
        )}

        {/* 详情弹窗 */}
        <Modal
          title="数据详情"
          open={detailModalVisible}
          onCancel={() => setDetailModalVisible(false)}
          footer={selectedDetail ? [
            <Button
              key="rpa-rerun"
              type="primary"
              danger
              disabled={selectedDetail.db_exists}
              onClick={() => handleRerunRpa(selectedDetail)}
            >
              RPA重跑
            </Button>,
            <Button
              key="dw-refresh"
              type="primary"
              disabled={selectedDetail.dw_exists}
              onClick={() => handleRefreshDw(selectedDetail)}
            >
              数仓刷数
            </Button>,
            <Button key="cancel" onClick={() => setDetailModalVisible(false)}>
              关闭
            </Button>
          ] : null}
          width={600}
        >
          {selectedDetail && (
            <Descriptions column={2} bordered>
              <Descriptions.Item label="品牌">{selectedDetail.brand}</Descriptions.Item>
              <Descriptions.Item label="日期">
                {selectedDetail.date && selectedDetail.date.length === 8
                  ? `${selectedDetail.date.slice(0, 4)}-${selectedDetail.date.slice(4, 6)}-${selectedDetail.date.slice(6, 8)}`
                  : selectedDetail.date}
              </Descriptions.Item>
              <Descriptions.Item label="数据库状态">
                <Tag color={selectedDetail.db_exists ? 'green' : 'red'}>
                  {selectedDetail.db_exists ? '存在' : '不存在'}
                </Tag>
              </Descriptions.Item>
              <Descriptions.Item label="数仓状态">
                <Tag color={selectedDetail.dw_exists ? 'blue' : 'orange'}>
                  {selectedDetail.dw_exists ? '存在' : '未同步'}
                </Tag>
              </Descriptions.Item>
              <Descriptions.Item label="数据库数据条数">{selectedDetail.db_data_count || 0}</Descriptions.Item>
              <Descriptions.Item label="数仓数据条数">{selectedDetail.dw_data_count || 0}</Descriptions.Item>
              <Descriptions.Item label="数据表名">{selectedDetail.data_table_name || '-'}</Descriptions.Item>
              <Descriptions.Item label="数仓表名">{selectedDetail.dw_table_name || '-'}</Descriptions.Item>
              <Descriptions.Item label="数仓刷新时间" span={2}>
                {selectedDetail.dw_refresh_time || '-'}
              </Descriptions.Item>
              {selectedDetail.biz_name_lvl1 && (
                <Descriptions.Item label="一级业务">{selectedDetail.biz_name_lvl1}</Descriptions.Item>
              )}
              {selectedDetail.biz_name_lvl2 && (
                <Descriptions.Item label="二级业务">{selectedDetail.biz_name_lvl2}</Descriptions.Item>
              )}
            </Descriptions>
          )}
        </Modal>
      </Card>
    </PageContainer>
  );
};

export default RtbMonitorPage;



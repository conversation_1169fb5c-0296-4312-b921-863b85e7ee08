import React, { useState, useEffect } from 'react';
import { PageContainer } from '@ant-design/pro-components';
import { Card, Select, DatePicker, Button, Input, message, Spin, Alert, Modal } from 'antd';
import { ReloadOutlined, SearchOutlined } from '@ant-design/icons';
import dayjs from 'dayjs';
import { getMonitor, getBrandTrend } from '@/services/monitor';
import ExcelFreezeTable from '@/components/ExcelFreezeTable';
import BrandTrendPopover from '@/components/BrandTrendPopover';
import DetailModal from '@/components/DetailModal';

const { RangePicker } = DatePicker;
const { Option } = Select;

// 定义数据类型
interface MatrixData {
  brands: string[];
  dates: string[];
  matrix: {
    brand: string;
    dates: {
      status: number;
      db_exists: boolean;
      dw_exists: boolean;
      db_data_count: number;
      dw_data_count: number;
      data_table_name?: string;
      dw_table_name?: string;
      dw_refresh_time?: string;
      biz_name_lvl1?: string;
      biz_name_lvl2?: string;
      raw_data?: any;
    }[];
  }[];
  stats: {
    total_brands: number;
    total_platforms: number;
    anomaly_count: number;
    today_completion_rate: string;
  };
}

// 获取默认日期范围的函数
function getDefaultDateRange(periodType: string = '日') {
  const today = dayjs();
  if (periodType === '月') {
    // 月维度：当前月份往前推12个月
    const endMonth = today.subtract(1, 'month'); // T-1月
    const startMonth = endMonth.subtract(11, 'month'); // 往前推11个月，总共12个月
    return [startMonth, endMonth];
  } else {
    // 日维度：T-1日往前推15天
    const yesterday = today.subtract(1, 'day');
    const startDate = yesterday.subtract(14, 'day');
    return [startDate, yesterday];
  }
}

const IndustryMonitor: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [matrixData, setMatrixData] = useState<MatrixData | null>(null);

  // 业务选项状态
  const [biz2Options, setBiz2Options] = useState<string[]>([]);
  const [periodTypeOptions, setPeriodTypeOptions] = useState<string[]>([]);
  const [optionsLoading, setOptionsLoading] = useState(false);

  // 筛选条件
  const [dateRange, setDateRange] = useState<any>(getDefaultDateRange('日'));
  const [brand, setBrand] = useState('');
  const [biz2, setBiz2] = useState(''); // 二级业务，默认为空
  const [periodType, setPeriodType] = useState('日'); // 周期维度，默认选中"日"
  const [selectedDetail, setSelectedDetail] = useState<any>(null);
  const [detailModalVisible, setDetailModalVisible] = useState(false);

  // 获取业务选项
  const fetchBusinessOptions = async () => {
    setOptionsLoading(true);
    try {
      const response = await fetch('/api/business_options?biz_name_lvl1=行业数据');
      const json = await response.json();
      if (json.code === 0) {
        setBiz2Options(json.data.biz_lvl2_options || []);
        setPeriodTypeOptions(json.data.period_type_options || []);
        // 设置默认的二级业务选项
        if (json.data.biz_lvl2_options && json.data.biz_lvl2_options.length > 0) {
          setBiz2(json.data.biz_lvl2_options[0]);
        }
      } else {
        message.error(json.msg || '获取业务选项失败');
      }
    } catch (e: any) {
      message.error('获取业务选项失败: ' + (e.message || String(e)));
    } finally {
      setOptionsLoading(false);
    }
  };

  // 周期类型变化时更新日期范围
  const handlePeriodTypeChange = (value: string) => {
    setPeriodType(value);
    setDateRange(getDefaultDateRange(value));
  };

  const fetchData = async () => {
    setLoading(true);
    setError('');
    try {
      const query: any = {};

      // 行业数据监控页面只显示行业数据
      query.biz_name_lvl1 = '行业数据';

      // 只传递有值的参数
      if (brand && brand.trim()) {
        query.brand = brand.trim();
      }
      if (biz2 && biz2.trim()) {
        query.biz_name_lvl2 = biz2.trim();
      }
      if (periodType && periodType.trim()) {
        query.period_type = periodType.trim();
      }
      if (dateRange && dateRange[0] && dateRange[1]) {
        if (periodType === '月') {
          // 月维度：使用YYYY-MM格式
          query.start_date = dateRange[0].format('YYYY-MM');
          query.end_date = dateRange[1].format('YYYY-MM');
        } else {
          // 日维度：使用YYYY-MM-DD格式
          query.start_date = dateRange[0].format('YYYY-MM-DD');
          query.end_date = dateRange[1].format('YYYY-MM-DD');
        }
      }

      const json = await getMonitor(query);
      if (json.code !== 0) throw new Error(json.msg || '接口返回错误');
      console.log('Matrix data received:', json.data);
      setMatrixData(json.data);
    } catch (e: any) {
      setError(e.message || String(e));
    } finally {
      setLoading(false);
    }
  };

  // 初始化时获取业务选项
  useEffect(() => {
    fetchBusinessOptions();
  }, []);

  // 业务选项加载完成后获取数据
  useEffect(() => {
    if (biz2Options.length > 0 && periodTypeOptions.length > 0) {
      fetchData();
    }
  }, [biz2Options, periodTypeOptions, biz2, periodType, dateRange, brand]);

  const handleCellClick = (brandData: any, dateData: any, date: string) => {
    setSelectedDetail({
      brand: brandData.brand,
      date: date,
      ...dateData
    });
    setDetailModalVisible(true);
  };

  const getStatusText = (status: number) => {
    switch (status) {
      case 0: return '无数据';
      case 1: return '部分数据';
      case 2: return '数据正常';
      case 3: return '数据不一致';
      default: return '未知状态';
    }
  };

  const renderStatusIcon = (status: number) => {
    const colors = ['#ff4d4f', '#faad14', '#52c41a', '#1890ff'];
    return (
      <div
        style={{
          width: 20,
          height: 20,
          borderRadius: '50%',
          backgroundColor: colors[status] || '#d9d9d9',
          margin: '0 auto'
        }}
      />
    );
  };

  const formatDate = (date: string) => {
    if (periodType === '月') {
      // 月维度：显示YYYY-MM格式
      return date;
    } else {
      // 日维度：显示MM-DD格式
      if (date.length === 8) {
        return `${date.slice(4, 6)}-${date.slice(6, 8)}`;
      } else if (date.length === 10) {
        return date.slice(5);
      }
      return date;
    }
  };

  const extractBrandName = (brand: string) => {
    if (brand.includes('(') && brand.includes(')')) {
      return brand.split('(')[0];
    }
    return brand;
  };

  const getFilteredMatrixData = () => {
    if (!matrixData) return null;
    
    // 如果没有选择品牌筛选，返回所有数据
    if (!brand || !brand.trim()) {
      return matrixData;
    }

    // 根据品牌名称筛选
    const filteredMatrix = matrixData.matrix.filter((brandData: any) => {
      const brandName = extractBrandName(brandData.brand).toLowerCase();
      return brandName.includes(brand.toLowerCase());
    });

    return {
      ...matrixData,
      matrix: filteredMatrix,
      stats: {
        ...matrixData.stats,
        total_brands: filteredMatrix.length
      }
    };
  };

  return (
    <PageContainer
      title="行业数据监控"
      subTitle="监控行业数据的采集和处理状态"
    >
      <Card>
        <div style={{ marginBottom: 16, display: 'flex', gap: 16, flexWrap: 'wrap', alignItems: 'center' }}>
          <div>
            <span style={{ marginRight: 8 }}>二级业务:</span>
            <Select
              value={biz2}
              onChange={setBiz2}
              style={{ width: 200 }}
              loading={optionsLoading}
              placeholder="请选择二级业务"
            >
              {biz2Options.map(option => (
                <Option key={option} value={option}>{option}</Option>
              ))}
            </Select>
          </div>
          
          <div>
            <span style={{ marginRight: 8 }}>周期维度:</span>
            <Select
              value={periodType}
              onChange={handlePeriodTypeChange}
              style={{ width: 120 }}
              loading={optionsLoading}
            >
              {periodTypeOptions.map(option => (
                <Option key={option} value={option}>{option}</Option>
              ))}
            </Select>
          </div>

          <div>
            <span style={{ marginRight: 8 }}>日期范围:</span>
            <RangePicker
              value={dateRange}
              onChange={setDateRange}
              picker={periodType === '月' ? 'month' : 'date'}
              format={periodType === '月' ? 'YYYY-MM' : 'YYYY-MM-DD'}
            />
          </div>

          <div>
            <span style={{ marginRight: 8 }}>品牌筛选:</span>
            <Input
              value={brand}
              onChange={(e) => setBrand(e.target.value)}
              placeholder="输入品牌名称筛选"
              style={{ width: 200 }}
              prefix={<SearchOutlined />}
            />
          </div>

          <Button
            type="primary"
            icon={<ReloadOutlined />}
            onClick={fetchData}
            loading={loading}
          >
            刷新
          </Button>
        </div>

        {error && (
          <Alert
            message="数据加载失败"
            description={error}
            type="error"
            showIcon
            style={{ marginBottom: 16 }}
          />
        )}

        {matrixData && (
          <div style={{ marginBottom: 16 }}>
            <div style={{ display: 'flex', gap: 24, fontSize: 14 }}>
              <span>品牌数量: <strong>{getFilteredMatrixData()?.stats.total_brands || 0}</strong></span>
              <span>异常数量: <strong style={{ color: '#ff4d4f' }}>{matrixData.stats.anomaly_count}</strong></span>
              <span>完成率: <strong style={{ color: '#52c41a' }}>{matrixData.stats.today_completion_rate}</strong></span>
            </div>
          </div>
        )}

        <Spin spinning={loading}>
          {matrixData && getFilteredMatrixData() && (
            <ExcelFreezeTable
              headers={matrixData.dates}
              rows={getFilteredMatrixData()!.matrix.map((brandData) => ({
                brand: brandData.brand,
                cells: brandData.dates.map((dateData, dateIndex) => (
                  <div
                    className="status-icon-cell"
                    onClick={() => handleCellClick(brandData, dateData, matrixData.dates[dateIndex])}
                    title={getStatusText(dateData.status)}
                  >
                    {renderStatusIcon(dateData.status)}
                  </div>
                ))
              }))}
              formatHeader={formatDate}
              extractBrandName={extractBrandName}
              renderBrandCell={(brand: string) => (
                <BrandTrendPopover
                  brand={brand}
                  dateRange={dateRange ? [
                    periodType === '月' ? dateRange[0].format('YYYY-MM') : dateRange[0].format('YYYY-MM-DD'),
                    periodType === '月' ? dateRange[1].format('YYYY-MM') : dateRange[1].format('YYYY-MM-DD')
                  ] : undefined}
                  biz_name_lvl1="行业数据"
                  biz_name_lvl2={biz2}
                  period_type={periodType}
                >
                  <span className="brand-name-hover">{extractBrandName(brand)}</span>
                </BrandTrendPopover>
              )}
            />
          )}
        </Spin>
      </Card>

      <DetailModal
        visible={detailModalVisible}
        onCancel={() => setDetailModalVisible(false)}
        detail={selectedDetail}
        biz_name_lvl1="行业数据"
      />
    </PageContainer>
  );
};

export default IndustryMonitor;

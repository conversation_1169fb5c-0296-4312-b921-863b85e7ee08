import { PageContainer } from '@ant-design/pro-components';
import { Card, Alert, DatePicker, Input, Button, Space, Row, Col, Statistic, Radio, Spin, Modal, Descriptions, Tag, message } from 'antd';
import { CheckCircleTwoTone, ExclamationCircleTwoTone, CloseCircleTwoTone } from '@ant-design/icons';
import React, { useState, useEffect } from 'react';
import dayjs from 'dayjs';
import isSameOrBefore from 'dayjs/plugin/isSameOrBefore';
import { getMonitor } from '@/services/monitor';
import BrandTrendPopover from '@/components/BrandTrendPopover';
import ExcelFreezeTable from '@/components/ExcelFreezeTable';

dayjs.extend(isSameOrBefore);

const { RangePicker } = DatePicker;

const CustomerMonitorPage: React.FC = () => {
  const [matrixData, setMatrixData] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  
  // 初始化默认日期范围：根据周期类型设置
  const getDefaultDateRange = (period: string = '日') => {
    if (period === '月') {
      // 月维度：2024年1月至T-1月
      const lastMonth = dayjs().subtract(1, 'month').startOf('month');
      const startMonth = dayjs('2024-01-01').startOf('month');
      return [startMonth, lastMonth];
    } else {
      // 日维度：T-1日往前推15天
      const yesterday = dayjs().subtract(1, 'day');
      const startDate = yesterday.subtract(14, 'day');
      return [startDate, yesterday];
    }
  };

  const [dateRange, setDateRange] = useState<any>(getDefaultDateRange('日'));
  const [brand, setBrand] = useState('');
  const [periodType, setPeriodType] = useState('日'); // 日/月，默认选中"日"
  const [selectedDetail, setSelectedDetail] = useState<any>(null);
  const [detailModalVisible, setDetailModalVisible] = useState(false);

  // 周期类型选项
  const periodTypeOptions = [
    { label: '日', value: '日' },
    { label: '月', value: '月' },
  ];

  const fetchData = async () => {
    setLoading(true);
    setError('');
    try {
      const query: any = {};

      // 新老客数据监控页面只显示新老客数据
      query.biz_name_lvl1 = '新老客数据';

      // 只传递有值的参数
      if (brand && brand.trim()) {
        query.brand = brand.trim();
      }
      if (periodType && periodType.trim()) {
        query.period_type = periodType.trim();
      }
      if (dateRange && dateRange[0] && dateRange[1]) {
        if (periodType === '月') {
          query.start_date = dateRange[0].format('YYYY-MM-DD');
          query.end_date = dateRange[1].format('YYYY-MM-DD');
        } else {
          query.start_date = dateRange[0].format('YYYY-MM-DD');
          query.end_date = dateRange[1].format('YYYY-MM-DD');
        }
      }

      console.log('=== 新老客数据监控页面 - 查询参数 ===');
      console.log('查询参数:', query);

      const json = await getMonitor(query);
      if (json.code !== 0) throw new Error(json.msg || '接口返回错误');
      console.log('=== 新老客数据监控页面 - 数据接收 ===');
      console.log(`原始数据: ${json.data.matrix.length} 个品牌`);
      console.log('Customer data received:', json.data);
      setMatrixData(json.data);
    } catch (e: any) {
      setError(e.message || String(e));
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchData();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const onSearch = () => {
    fetchData();
  };

  // 处理周期类型切换
  const handlePeriodTypeChange = (e: any) => {
    const newPeriodType = e.target.value;
    setPeriodType(newPeriodType);

    // 立即更新日期范围选择控件的显示，但不查询数据
    const newDateRange = getDefaultDateRange(newPeriodType);
    setDateRange(newDateRange);

    // 不自动查询数据，保持表头和数据矩阵不变
  };

  // 获取状态文本
  const getStatusText = (status: number) => {
    switch (status) {
      case 0: return '无数据';
      case 1: return '部分数据';
      case 2: return '完整数据';
      case 3: return '数据不一致';
      default: return '未知';
    }
  };

  // 渲染状态图标
  const renderStatusIcon = (status: number) => {
    switch (status) {
      case 0:
        return <CloseCircleTwoTone twoToneColor="#ff4d4f" style={{ fontSize: '24px' }} />;
      case 1:
        return <ExclamationCircleTwoTone twoToneColor="#faad14" style={{ fontSize: '24px' }} />;
      case 2:
        return <CheckCircleTwoTone twoToneColor="#52c41a" style={{ fontSize: '24px' }} />;
      case 3:
        return <ExclamationCircleTwoTone twoToneColor="#1890ff" style={{ fontSize: '24px' }} />;
      default:
        return <CloseCircleTwoTone twoToneColor="#d9d9d9" style={{ fontSize: '24px' }} />;
    }
  };

  // 处理单元格点击事件
  const handleCellClick = (brandData: any, dateData: any, date: string) => {
    setSelectedDetail({
      brand: brandData.brand,
      date: date,
      ...dateData
    });
    setDetailModalVisible(true);
  };

  // 格式化日期显示 - 根据数据格式自动判断，不依赖periodType状态
  const formatDate = (dateStr: string) => {
    // 根据数据本身的格式来判断如何显示，而不是依赖periodType状态
    if (dateStr.length === 7 && dateStr.includes('-')) {
      // 已经是YYYY-MM格式，说明是月维度数据
      return dateStr;
    } else if (dateStr.length === 6) {
      // YYYYMM格式，说明是月维度数据，转换为YYYY-MM
      return `${dateStr.slice(0, 4)}-${dateStr.slice(4, 6)}`;
    } else if (dateStr.length === 8) {
      // YYYYMMDD格式，说明是日维度数据，显示为MM/DD
      return `${dateStr.slice(4, 6)}/${dateStr.slice(6, 8)}`;
    }
    return dateStr;
  };

  // 提取品牌名称（去掉平台信息）
  const extractBrandName = (fullBrandName: string) => {
    const match = fullBrandName.match(/^(.+?)\(/);
    return match ? match[1] : fullBrandName;
  };

  // 提取平台名称
  const extractPlatformName = (fullBrandName: string) => {
    const match = fullBrandName.match(/\((.+?)\)$/);
    return match ? match[1] : undefined;
  };

  // 数仓刷数处理函数
  const handleRefreshDw = async (record: any) => {
    try {
      const response = await fetch('/api/refresh_dw', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(record.raw_data || record),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      if (result.code === 0) {
        message.success(result.msg || '数仓刷数请求已提交成功！');
        setDetailModalVisible(false);
        // 刷新数据
        fetchData();
      } else {
        throw new Error(result.msg || '数仓刷数失败');
      }
    } catch (error: any) {
      message.error(`数仓刷数失败: ${error.message}`);
    }
  };

  return (
    <PageContainer header={{ title: '新老客数据监控' }}>
      <Card>
        {/* 筛选器 */}
        <div style={{ marginBottom: 16 }}>
          {/* 第一行：周期维度选择 */}
          <div style={{ marginBottom: 12 }}>
            <div style={{
              display: 'inline-flex',
              alignItems: 'center',
              gap: 8,
              padding: '8px 16px',
              backgroundColor: '#e6f7ff',
              borderRadius: '8px',
              border: '1px solid #91d5ff'
            }}>
              <span style={{ fontSize: '14px', color: '#1890ff', fontWeight: 600 }}>周期维度：</span>
              <Radio.Group
                options={periodTypeOptions}
                value={periodType}
                onChange={handlePeriodTypeChange}
                size="middle"
                buttonStyle="solid"
              />
            </div>
          </div>

          {/* 第二行：基础筛选 */}
          <Space wrap style={{ marginBottom: 12 }}>
            <span>{periodType === '月' ? '月份范围：' : '日期范围：'}</span>
            {periodType === '月' ? (
              <RangePicker
                value={dateRange}
                onChange={setDateRange}
                picker="month"
                allowClear
                placeholder={['开始月份', '结束月份']}
              />
            ) : (
              <RangePicker
                value={dateRange}
                onChange={setDateRange}
                allowClear
                placeholder={['开始日期', '结束日期']}
              />
            )}
            <span>品牌：</span>
            <Input placeholder="品牌" value={brand} onChange={(e) => setBrand(e.target.value)} style={{ width: 160 }} />
            <Button type="primary" onClick={onSearch}>查询</Button>
          </Space>
        </div>

        <div style={{ marginBottom: 16, fontSize: 12, color: '#666' }}>
          当前查询范围: {periodType === '月'
            ? `${dateRange?.[0]?.format('YYYY-MM')} 至 ${dateRange?.[1]?.format('YYYY-MM')}`
            : `${dateRange?.[0]?.format('YYYY-MM-DD')} 至 ${dateRange?.[1]?.format('YYYY-MM-DD')}`
          } |
          周期: {periodType} (新老客数据)
        </div>

        {error && <Alert type="error" message={`加载失败：${error}`} style={{ marginBottom: 12 }} />}

        {/* 统计信息 */}
        {matrixData && (
          <Row gutter={16} className="stats-cards">
            <Col span={6}>
              <Card>
                <Statistic title="新老客品牌数量" value={matrixData.stats.total_brands} />
              </Card>
            </Col>
            <Col span={6}>
              <Card>
                <Statistic title="覆盖平台" value={matrixData.stats.total_platforms} />
              </Card>
            </Col>
            <Col span={6}>
              <Card>
                <Statistic title="异常数量" value={matrixData.stats.anomaly_count} />
              </Card>
            </Col>
            <Col span={6}>
              <Card>
                <Statistic title="任务成功率" value={matrixData.stats.today_completion_rate} />
              </Card>
            </Col>
          </Row>
        )}

        {/* 数据状态矩阵 */}
        <Spin spinning={loading}>
          {matrixData && matrixData.matrix && matrixData.matrix.length > 0 ? (
            <Card
              title={
                <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                  <span>数据状态矩阵 <span style={{ fontSize: '12px', color: '#999', fontWeight: 'normal' }}>(每10分钟刷新近15天数据状态)</span></span>
                  <div style={{ display: 'flex', alignItems: 'center', gap: 16, fontSize: 12 }}>
                    <div style={{ display: 'flex', alignItems: 'center', gap: 4 }}>
                      <CheckCircleTwoTone twoToneColor="#52c41a" style={{ fontSize: '16px' }} />
                      <span>有数据且DB与DW完全一致</span>
                    </div>
                    <div style={{ display: 'flex', alignItems: 'center', gap: 4 }}>
                      <ExclamationCircleTwoTone twoToneColor="#1890ff" style={{ fontSize: '16px' }} />
                      <span>有数据但DB与DW不一致</span>
                    </div>
                    <div style={{ display: 'flex', alignItems: 'center', gap: 4 }}>
                      <ExclamationCircleTwoTone twoToneColor="#faad14" style={{ fontSize: '16px' }} />
                      <span>DB有数据但DW无数据</span>
                    </div>
                    <div style={{ display: 'flex', alignItems: 'center', gap: 4 }}>
                      <CloseCircleTwoTone twoToneColor="#ff4d4f" style={{ fontSize: '16px' }} />
                      <span>DB与DW都无数据</span>
                    </div>
                    <div style={{ color: '#999', fontSize: 11, marginLeft: 8 }}>
                      DB=数据库 DW=数据仓库
                    </div>
                  </div>
                </div>
              }
              loading={loading}
            >
              <ExcelFreezeTable
                headers={matrixData.dates}
                rows={matrixData.matrix.map((brandData: any) => ({
                  brand: brandData.brand,
                  cells: brandData.dates.map((dateData: any, dateIndex: number) => (
                    <div
                      className="status-icon-cell"
                      onClick={() => handleCellClick(brandData, dateData, matrixData.dates[dateIndex])}
                      title={getStatusText(dateData.status)}
                    >
                      {renderStatusIcon(dateData.status)}
                    </div>
                  ))
                }))}
                formatHeader={formatDate}
                extractBrandName={extractBrandName}
                renderBrandCell={(brand: string) => (
                  <BrandTrendPopover
                    brand={extractBrandName(brand)}
                    platform={extractPlatformName(brand)}
                    dateRange={dateRange ? [dateRange[0].format('YYYY-MM-DD'), dateRange[1].format('YYYY-MM-DD')] : undefined}
                    biz_name_lvl1="新老客数据"
                  >
                    <span className="brand-name-hover">{extractBrandName(brand)}</span>
                  </BrandTrendPopover>
                )}
                className="matrix-table"
              />
            </Card>
          ) : (
            !loading && (
              <Alert 
                type="info" 
                message="暂无新老客数据" 
                description="数据库中没有找到 biz_name_lvl1='新老客数据' 的记录，请检查数据源配置。" 
                style={{ marginTop: 16 }}
              />
            )
          )}
        </Spin>

        {/* 详情弹窗 */}
        <Modal
          title="数据详情"
          open={detailModalVisible}
          onCancel={() => setDetailModalVisible(false)}
          footer={[
            <Button key="close" onClick={() => setDetailModalVisible(false)}>
              关闭
            </Button>,
            selectedDetail?.db_exists && !selectedDetail?.dw_exists && (
              <Button key="refresh" type="primary" onClick={() => handleRefreshDw(selectedDetail)}>
                数仓刷数
              </Button>
            ),
          ].filter(Boolean)}
          width={600}
        >
          {selectedDetail && (
            <Descriptions column={2} bordered>
              <Descriptions.Item label="品牌">{extractBrandName(selectedDetail.brand)}</Descriptions.Item>
              <Descriptions.Item label="日期">
                {selectedDetail.date && selectedDetail.date.length === 8
                  ? `${selectedDetail.date.slice(0, 4)}-${selectedDetail.date.slice(4, 6)}-${selectedDetail.date.slice(6, 8)}`
                  : selectedDetail.date}
              </Descriptions.Item>
              <Descriptions.Item label="数据库状态">
                <Tag color={selectedDetail.db_exists ? 'green' : 'red'}>
                  {selectedDetail.db_exists ? '存在' : '不存在'}
                </Tag>
              </Descriptions.Item>
              <Descriptions.Item label="数仓状态">
                <Tag color={selectedDetail.dw_exists ? 'blue' : 'orange'}>
                  {selectedDetail.dw_exists ? '存在' : '未同步'}
                </Tag>
              </Descriptions.Item>
              <Descriptions.Item label="数据库数据条数">{selectedDetail.db_data_count || 0}</Descriptions.Item>
              <Descriptions.Item label="数仓数据条数">{selectedDetail.dw_data_count || 0}</Descriptions.Item>
              <Descriptions.Item label="数据表名">{selectedDetail.data_table_name || '-'}</Descriptions.Item>
              <Descriptions.Item label="数仓表名">{selectedDetail.dw_table_name || '-'}</Descriptions.Item>
              <Descriptions.Item label="数仓刷新时间" span={2}>
                {selectedDetail.dw_refresh_time || '-'}
              </Descriptions.Item>
              {selectedDetail.biz_name_lvl1 && (
                <Descriptions.Item label="一级业务">{selectedDetail.biz_name_lvl1}</Descriptions.Item>
              )}
              {selectedDetail.biz_name_lvl2 && (
                <Descriptions.Item label="二级业务">{selectedDetail.biz_name_lvl2}</Descriptions.Item>
              )}
            </Descriptions>
          )}
        </Modal>
      </Card>
    </PageContainer>
  );
};

export default CustomerMonitorPage;

import { PageContainer, ProTable } from '@ant-design/pro-components';
import { useRequest } from '@umijs/max';
import { Tag } from 'antd';
import type { ProColumns } from '@ant-design/pro-components';

// 定义数据类型
interface DataItem {
  date: string;
  platform: string;
  value: number;
  is_anomaly: boolean;
}

// API请求函数
const getChartData = async () => {
  try {
    const response = await fetch('/api/chart_data').then(res => res.json());
    return { data: response, success: true };
  } catch (error) {
    return { data: [], success: false };
  }
};

const HomePage = () => {
  const columns: ProColumns<DataItem>[] = [
    { title: '日期', dataIndex: 'date', valueType: 'date', sorter: true },
    { title: '平台', dataIndex: 'platform', valueType: 'text' },
    { title: '数据量', dataIndex: 'value', sorter: true },
    {
      title: '状态',
      dataIndex: 'is_anomaly',
      render: (_, record) => (
        record.is_anomaly ? <Tag color="volcano">数据异常</Tag> : <Tag color="green">正常</Tag>
      ),
    },
  ];

  return (
    <PageContainer title="首页 - 数据趋势概览">
      <ProTable<DataItem>
        columns={columns}
        request={getChartData}
        rowKey={(record) => `${record.date}-${record.platform}`}
        search={false}
        pagination={{
          pageSize: 15,
        }}
        headerTitle="数据记录趋势"
      />
    </PageContainer>
  );
};

export default HomePage;


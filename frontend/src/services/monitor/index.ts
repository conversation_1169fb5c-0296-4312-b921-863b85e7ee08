export type MonitorQuery = {
  biz_name_lvl1?: string;
  biz_name_lvl2?: string;
  platform?: string;
  brand?: string;
  date?: string; // YYYY-MM-DD
  start_date?: string; // YYYY-MM-DD
  end_date?: string; // YYYY-MM-DD
  limit?: number;
  offset?: number;
};

export type MonitorRow = {
  id: number;
  biz_name_lvl1: string;
  biz_name_lvl2: string;
  date: string;
  platform: string;
  brand: string;
  data_table_name?: string;
  db_exists: number;
  dw_table_name?: string;
  dw_exists: number;
  dw_data_count: number;
  db_data_count: number;
  dw_refresh_time?: string;
};

const API_BASE = '';

export async function getMonitor(query: MonitorQuery): Promise<{
  code: number;
  msg?: string;
  data: any; // 改为any类型以支持新的矩阵数据结构
}> {
  const params = new URLSearchParams();
  Object.entries(query).forEach(([key, value]) => {
    if (value !== undefined && value !== null && String(value) !== '') {
      params.append(key, String(value));
    }
  });
  const resp = await fetch(`${API_BASE}/api/monitor?${params.toString()}`);
  if (!resp.ok) throw new Error(`HTTP ${resp.status}`);
  return resp.json();
}

export type BrandTrendQuery = {
  brand: string;
  platform?: string;
  start_date?: string;
  end_date?: string;
  biz_name_lvl2?: string;
};

export async function getBrandTrend(query: BrandTrendQuery): Promise<{
  code: number;
  msg?: string;
  data: {
    dates: string[];
    db_counts: number[];
    dw_counts: number[];
    brand: string;
  };
}> {
  const params = new URLSearchParams();
  Object.entries(query).forEach(([key, value]) => {
    if (value !== undefined && value !== null && String(value) !== '') {
      params.append(key, String(value));
    }
  });
  const resp = await fetch(`${API_BASE}/api/brand_trend?${params.toString()}`);
  if (!resp.ok) throw new Error(`HTTP ${resp.status}`);
  return resp.json();
}



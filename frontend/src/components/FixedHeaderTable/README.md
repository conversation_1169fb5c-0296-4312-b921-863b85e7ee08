# FixedHeaderTable 固定表头表格组件

一个支持固定首行（表头）和首列（品牌列）的React表格组件，专为数据状态矩阵设计。

## 功能特点

- ✅ **固定表头**：水平滚动时表头始终可见
- ✅ **固定首列**：垂直滚动时品牌列始终可见  
- ✅ **同步滚动**：表头和品牌列与主内容区域滚动同步
- ✅ **响应式设计**：适配不同屏幕尺寸
- ✅ **交互效果**：悬停高亮和点击反馈
- ✅ **自定义渲染**：支持自定义表头和品牌单元格渲染

## 使用方法

```tsx
import FixedHeaderTable from '@/components/FixedHeaderTable';

// 基本用法
<FixedHeaderTable
  headers={['08/15', '08/16', '08/17', '08/18']}
  rows={[
    {
      brand: '三养(美团)',
      cells: [
        <StatusIcon status={0} />,
        <StatusIcon status={1} />,
        <StatusIcon status={2} />,
        <StatusIcon status={3} />
      ]
    },
    {
      brand: '三胖蛋(美团)',
      cells: [
        <StatusIcon status={2} />,
        <StatusIcon status={2} />,
        <StatusIcon status={1} />,
        <StatusIcon status={0} />
      ]
    }
  ]}
  formatHeader={(header) => header}
  extractBrandName={(brand) => brand.split('(')[0]}
  renderBrandCell={(brand) => (
    <BrandTrendPopover brand={brand}>
      <span className="brand-name-hover">{brand}</span>
    </BrandTrendPopover>
  )}
/>
```

## API

### Props

| 参数 | 说明 | 类型 | 默认值 |
|------|------|------|--------|
| headers | 表头数据数组 | `string[]` | - |
| rows | 行数据数组 | `Array<{brand: string, cells: React.ReactNode[]}>` | - |
| onCellClick | 单元格点击回调 | `(rowIndex: number, cellIndex: number, cellData: any) => void` | - |
| formatHeader | 表头格式化函数 | `(header: string) => string` | `(header) => header` |
| extractBrandName | 品牌名提取函数 | `(brand: string) => string` | `(brand) => brand` |
| renderBrandCell | 品牌单元格自定义渲染 | `(brand: string, rowIndex: number) => React.ReactNode` | - |
| className | 自定义CSS类名 | `string` | `''` |

### 数据结构

#### headers
表头数据，通常是日期字符串数组：
```typescript
['20250815', '20250816', '20250817', ...]
```

#### rows
行数据，每行包含品牌名和单元格数据：
```typescript
[
  {
    brand: '三养(美团)',
    cells: [
      <StatusIcon status={0} onClick={...} />,
      <StatusIcon status={1} onClick={...} />,
      // ...
    ]
  },
  // ...
]
```

## 样式定制

组件使用CSS类名进行样式控制，主要类名包括：

- `.fixed-header-table-container` - 容器
- `.fixed-corner` - 左上角固定区域
- `.fixed-header` - 固定表头区域
- `.fixed-brand-column` - 固定品牌列区域
- `.main-content` - 主内容区域
- `.brand-name-hover` - 品牌名悬停效果
- `.status-icon-cell` - 状态图标单元格

## 使用示例

### 在数据监控页面中使用

```tsx
// 全量数据监控页面
<FixedHeaderTable
  headers={matrixData.dates}
  rows={matrixData.matrix.map((brandData) => ({
    brand: brandData.brand,
    cells: brandData.dates.map((dateData, dateIndex) => (
      <div
        className="status-icon-cell"
        onClick={() => handleCellClick(brandData, dateData, matrixData.dates[dateIndex])}
        title={getStatusText(dateData.status)}
      >
        {renderStatusIcon(dateData.status)}
      </div>
    ))
  }))}
  formatHeader={formatDate}
  extractBrandName={extractBrandName}
  renderBrandCell={(brand) => (
    <BrandTrendPopover
      brand={brand}
      dateRange={dateRange ? [dateRange[0].format('YYYY-MM-DD'), dateRange[1].format('YYYY-MM-DD')] : undefined}
    >
      <span className="brand-name-hover">{extractBrandName(brand)}</span>
    </BrandTrendPopover>
  )}
  className="matrix-table"
/>
```

## 注意事项

1. **性能优化**：对于大量数据，建议使用虚拟滚动或分页
2. **滚动同步**：组件内部自动处理滚动同步，无需手动干预
3. **响应式**：在小屏幕设备上，品牌列宽度会自动调整
4. **事件处理**：单元格的点击事件应该在cell内容中处理，而不是通过onCellClick
5. **样式覆盖**：可以通过className传入自定义样式类名

## 浏览器兼容性

- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

支持现代浏览器的CSS Grid、Flexbox和position: sticky特性。

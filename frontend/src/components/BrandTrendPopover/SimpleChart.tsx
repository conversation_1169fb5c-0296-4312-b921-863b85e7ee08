import React, { useState } from 'react';

interface SimpleChartProps {
  data: {
    dates: string[];
    db_counts: number[];
    dw_counts: number[];
    brand: string;
  };
}

const SimpleChart: React.FC<SimpleChartProps> = ({ data }) => {
  const { dates, db_counts, dw_counts, brand } = data;
  const [tooltip, setTooltip] = useState<{
    visible: boolean;
    x: number;
    y: number;
    content: string;
  }>({ visible: false, x: 0, y: 0, content: '' });

  if (!dates.length) {
    return <div style={{ padding: 20, textAlign: 'center' }}>暂无数据</div>;
  }

  // 计算最大值用于缩放，确保最小值为1避免除零错误
  const maxValue = Math.max(...db_counts, ...dw_counts, 1);
  const chartHeight = 180; // 减小高度为横轴标签留空间
  const chartWidth = 320; // 减小宽度为纵轴标签留空间
  const leftMargin = 60; // 为纵轴标签预留空间
  const bottomMargin = 40; // 为横轴标签预留空间

  // 格式化数值显示
  const formatValue = (value: number) => {
    if (value >= 10000) {
      return (value / 10000).toFixed(1) + 'w';
    }
    return value.toString();
  };

  // 计算点的位置
  const getPointPosition = (index: number, value: number) => {
    const x = (index / (dates.length - 1)) * chartWidth;
    const y = chartHeight - (value / maxValue) * chartHeight;
    return { x, y };
  };

  // 处理鼠标悬浮事件
  const handleMouseMove = (event: React.MouseEvent, index: number) => {
    const rect = (event.currentTarget as SVGElement).getBoundingClientRect();
    const x = event.clientX - rect.left;
    const y = event.clientY - rect.top;

    const dbValue = db_counts[index];
    const dwValue = dw_counts[index];
    const date = dates[index];

    setTooltip({
      visible: true,
      x: x + 10,
      y: y - 10,
      content: `${date}\n数据库: ${formatValue(dbValue)}\n数仓: ${formatValue(dwValue)}`
    });
  };

  const handleMouseLeave = () => {
    setTooltip({ visible: false, x: 0, y: 0, content: '' });
  };

  // 生成路径字符串，处理0值数据
  const generatePath = (values: number[]) => {
    if (!values.length) return '';

    let path = '';
    values.forEach((value, index) => {
      const { x, y } = getPointPosition(index, value);
      if (index === 0) {
        path += `M ${x} ${y}`;
      } else {
        path += ` L ${x} ${y}`;
      }
    });
    return path;
  };

  // 检查是否有有效数据（非全零）
  const hasValidData = db_counts.some(v => v > 0) || dw_counts.some(v => v > 0);

  const dbPath = generatePath(db_counts);
  const dwPath = generatePath(dw_counts);

  return (
    <div style={{ padding: 16, width: 420, height: 320, position: 'relative' }}>
      <div style={{ textAlign: 'center', marginBottom: 16, fontSize: 14, fontWeight: 'bold' }}>
        {brand} 数据量走势
      </div>

      {!hasValidData && (
        <div style={{
          position: 'absolute',
          top: '50%',
          left: '50%',
          transform: 'translate(-50%, -50%)',
          color: '#999',
          fontSize: 12,
          zIndex: 10
        }}>
          该时间段内暂无数据
        </div>
      )}

      <div style={{ position: 'relative', marginLeft: leftMargin, marginBottom: bottomMargin }}>
        <svg
          width={chartWidth}
          height={chartHeight}
          style={{ border: '1px solid #f0f0f0' }}
          onMouseLeave={handleMouseLeave}
        >
          {/* 网格线 */}
          {[0, 0.25, 0.5, 0.75, 1].map((ratio) => (
            <line
              key={ratio}
              x1={0}
              y1={chartHeight * ratio}
              x2={chartWidth}
              y2={chartHeight * ratio}
              stroke="#f0f0f0"
              strokeWidth={1}
            />
          ))}

          {/* 数据库数据量线条 */}
          <path
            d={dbPath}
            fill="none"
            stroke="#1890ff"
            strokeWidth={2}
          />

          {/* 数仓数据量线条 */}
          <path
            d={dwPath}
            fill="none"
            stroke="#52c41a"
            strokeWidth={2}
          />

          {/* 数据点 - 添加鼠标事件，0值点用不同样式 */}
          {db_counts.map((value, index) => {
            const { x, y } = getPointPosition(index, value);
            return (
              <circle
                key={`db-${index}`}
                cx={x}
                cy={y}
                r={value === 0 ? 2 : 4}
                fill={value === 0 ? '#d9d9d9' : '#1890ff'}
                stroke={value === 0 ? '#1890ff' : 'none'}
                strokeWidth={value === 0 ? 1 : 0}
                style={{ cursor: 'pointer' }}
                onMouseMove={(e) => handleMouseMove(e, index)}
              />
            );
          })}

          {dw_counts.map((value, index) => {
            const { x, y } = getPointPosition(index, value);
            return (
              <circle
                key={`dw-${index}`}
                cx={x}
                cy={y}
                r={value === 0 ? 2 : 4}
                fill={value === 0 ? '#d9d9d9' : '#52c41a'}
                stroke={value === 0 ? '#52c41a' : 'none'}
                strokeWidth={value === 0 ? 1 : 0}
                style={{ cursor: 'pointer' }}
                onMouseMove={(e) => handleMouseMove(e, index)}
              />
            );
          })}

          {/* 透明的悬浮区域，用于更好的鼠标交互 */}
          {dates.map((_, index) => {
            const { x } = getPointPosition(index, 0);
            return (
              <rect
                key={`hover-${index}`}
                x={x - 10}
                y={0}
                width={20}
                height={chartHeight}
                fill="transparent"
                style={{ cursor: 'pointer' }}
                onMouseMove={(e) => handleMouseMove(e, index)}
              />
            );
          })}
        </svg>

        {/* Y轴标签 - 修复位置 */}
        <div style={{
          position: 'absolute',
          left: -leftMargin,
          top: 0,
          height: chartHeight,
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'space-between',
          fontSize: 10,
          width: leftMargin - 10,
          textAlign: 'right',
          paddingRight: 5
        }}>
          <span>{formatValue(maxValue)}</span>
          <span>{formatValue(Math.round(maxValue * 0.75))}</span>
          <span>{formatValue(Math.round(maxValue * 0.5))}</span>
          <span>{formatValue(Math.round(maxValue * 0.25))}</span>
          <span>0</span>
        </div>

        {/* X轴标签 - 修复拥挤问题 */}
        <div style={{
          position: 'absolute',
          top: chartHeight + 5,
          left: 0,
          width: chartWidth,
          height: bottomMargin - 5
        }}>
          {dates.map((date, index) => {
            const { x } = getPointPosition(index, 0);
            // 只显示部分日期标签，避免拥挤
            const shouldShow = dates.length <= 7 || index % Math.ceil(dates.length / 6) === 0 || index === dates.length - 1;
            if (!shouldShow) return null;

            return (
              <div
                key={index}
                style={{
                  position: 'absolute',
                  left: x - 15,
                  top: 0,
                  width: 30,
                  fontSize: 9,
                  textAlign: 'center',
                  transform: 'rotate(-45deg)',
                  transformOrigin: 'center top',
                  whiteSpace: 'nowrap'
                }}
              >
                {date.slice(5)}
              </div>
            );
          })}
        </div>
      </div>

      {/* 图例 */}
      <div style={{ display: 'flex', justifyContent: 'center', gap: 20, fontSize: 12, marginTop: 10 }}>
        <div style={{ display: 'flex', alignItems: 'center', gap: 4 }}>
          <div style={{ width: 12, height: 2, backgroundColor: '#1890ff' }}></div>
          <span>数据库数据量</span>
        </div>
        <div style={{ display: 'flex', alignItems: 'center', gap: 4 }}>
          <div style={{ width: 12, height: 2, backgroundColor: '#52c41a' }}></div>
          <span>数仓数据量</span>
        </div>
      </div>

      {/* 悬浮提示框 */}
      {tooltip.visible && (
        <div
          style={{
            position: 'absolute',
            left: tooltip.x,
            top: tooltip.y,
            backgroundColor: 'rgba(0, 0, 0, 0.8)',
            color: 'white',
            padding: '6px 8px',
            borderRadius: 4,
            fontSize: 11,
            whiteSpace: 'pre-line',
            pointerEvents: 'none',
            zIndex: 1000,
            boxShadow: '0 2px 8px rgba(0, 0, 0, 0.2)'
          }}
        >
          {tooltip.content}
        </div>
      )}
    </div>
  );
};

export default SimpleChart;

import React, { useState, useEffect, useRef } from 'react';
import { Popover, Spin, message, Button } from 'antd';
import SimpleChart from './SimpleChart';
import './index.less';

interface BrandTrendData {
  dates: string[];
  db_counts: number[];
  dw_counts: number[];
  brand: string;
}

interface BrandTrendPopoverProps {
  brand: string;
  platform?: string;
  dateRange?: [string, string];
  biz2?: string;
  biz_name_lvl1?: string;
  biz_name_lvl2?: string;
  period_type?: string;
  data_type?: string;
  children: React.ReactNode;
}

const BrandTrendPopover: React.FC<BrandTrendPopoverProps> = ({
  brand,
  platform,
  dateRange,
  biz2,
  biz_name_lvl1,
  biz_name_lvl2,
  period_type,
  data_type,
  children,
}) => {
  const [trendData, setTrendData] = useState<BrandTrendData | null>(null);
  const [loading, setLoading] = useState(false);
  const [visible, setVisible] = useState(false);
  const [error, setError] = useState<string>('');

  const fetchTrendData = async () => {
    if (!brand || loading) return;

    setLoading(true);
    setError('');
    try {
      const params = new URLSearchParams();
      params.append('brand', brand);

      if (platform) {
        params.append('platform', platform);
      }

      if (biz_name_lvl1) {
        params.append('biz_name_lvl1', biz_name_lvl1);
      }

      if (biz2 || biz_name_lvl2) {
        params.append('biz_name_lvl2', biz2 || biz_name_lvl2 || '');
      }

      if (period_type) {
        params.append('period_type', period_type);
      }

      if (data_type) {
        params.append('data_type', data_type);
      }

      // 如果有日期范围则使用，否则后端会使用默认范围（T-1日往前推15天）
      if (dateRange && dateRange[0] && dateRange[1]) {
        params.append('start_date', dateRange[0]);
        params.append('end_date', dateRange[1]);
      }

      const response = await fetch(`/api/brand_trend?${params.toString()}`);
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}`);
      }

      const result = await response.json();
      if (result.code === 0) {
        setTrendData(result.data);
        setError('');
      } else {
        throw new Error(result.msg || '获取数据失败');
      }
    } catch (error: any) {
      console.error('获取趋势数据失败:', error);
      setError(error.message || '网络请求失败');
      setTrendData(null);
    } finally {
      setLoading(false);
    }
  };

  const handleVisibleChange = (newVisible: boolean) => {
    setVisible(newVisible);
    if (newVisible && !trendData && !loading) {
      fetchTrendData();
    }
  };



  const content = (
    <div className="brand-trend-popover">
      {loading ? (
        <div className="loading-container">
          <Spin size="small" />
          <span style={{ marginLeft: 8 }}>加载中...</span>
        </div>
      ) : error ? (
        <div className="no-data">
          <div style={{ color: '#ff4d4f', marginBottom: 8 }}>加载失败</div>
          <div style={{ fontSize: '12px', color: '#999' }}>{error}</div>
          <Button size="small" type="link" onClick={fetchTrendData} style={{ padding: 0, marginTop: 4 }}>
            重试
          </Button>
        </div>
      ) : trendData && trendData.dates.length > 0 ? (
        <SimpleChart data={trendData} />
      ) : (
        <div className="no-data">暂无数据</div>
      )}
    </div>
  );

  return (
    <Popover
      content={content}
      title={null}
      trigger="hover"
      placement="right"
      open={visible}
      onOpenChange={handleVisibleChange}
      overlayClassName="brand-trend-popover-overlay"
      mouseEnterDelay={0.3}
      mouseLeaveDelay={0.1}
    >
      {children}
    </Popover>
  );
};

export default BrandTrendPopover;

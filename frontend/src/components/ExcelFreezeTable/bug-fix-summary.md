# 供给数据监控和新老客数据监控页面Bug修复总结

## 🐛 问题描述

1. **弹窗显示信息样式不对** - 点击矩阵中的图标，弹窗显示的信息样式与其他页面不一致
2. **品牌名称点击折线图数据不对** - 点击最左侧的品牌名称，弹窗中的折线图数据不正确

## 🔧 问题分析

### 问题1：弹窗样式不一致
**原因**：供给数据监控和新老客数据监控页面使用的是 `Table` 组件显示详情，而其他页面使用的是 `Descriptions` 组件。

**对比**：
- ❌ **错误样式**：使用 `Table` 组件，显示为表格形式
- ✅ **正确样式**：使用 `Descriptions` 组件，显示为描述列表形式，更美观

### 问题2：折线图数据不正确
**原因**：`BrandTrendPopover` 组件缺少 `biz_name_lvl2` 参数，导致API请求时无法获取正确的业务类型数据。

**对比**：
- ❌ **错误调用**：`<BrandTrendPopover brand={brand} dateRange={...} />`
- ✅ **正确调用**：`<BrandTrendPopover brand={brand} dateRange={...} biz_name_lvl2={biz2} />`

## ✅ 修复方案

### 修复1：统一弹窗样式

#### 供给数据监控页面 (`SupplyMonitor`)
```tsx
// 修改前
import { ..., Table, ... } from 'antd';

{selectedDetail && (
  <Table
    dataSource={[
      { key: '品牌', value: selectedDetail.brand },
      { key: '日期', value: selectedDetail.date },
      // ...
    ]}
    columns={[
      { title: '字段', dataIndex: 'key', width: 120 },
      { title: '值', dataIndex: 'value' },
    ]}
    pagination={false}
    size="small"
  />
)}

// 修改后
import { ..., Descriptions, Tag, ... } from 'antd';

{selectedDetail && (
  <Descriptions column={2} bordered>
    <Descriptions.Item label="品牌">{extractBrandName(selectedDetail.brand)}</Descriptions.Item>
    <Descriptions.Item label="日期">
      {selectedDetail.date && selectedDetail.date.length === 8
        ? `${selectedDetail.date.slice(0, 4)}-${selectedDetail.date.slice(4, 6)}-${selectedDetail.date.slice(6, 8)}`
        : selectedDetail.date}
    </Descriptions.Item>
    <Descriptions.Item label="数据库状态">
      <Tag color={selectedDetail.db_exists ? 'green' : 'red'}>
        {selectedDetail.db_exists ? '存在' : '不存在'}
      </Tag>
    </Descriptions.Item>
    <Descriptions.Item label="数仓状态">
      <Tag color={selectedDetail.dw_exists ? 'blue' : 'orange'}>
        {selectedDetail.dw_exists ? '存在' : '未同步'}
      </Tag>
    </Descriptions.Item>
    // ... 更多字段
  </Descriptions>
)}
```

#### 新老客数据监控页面 (`CustomerMonitor`)
应用相同的修复方案。

### 修复2：传递正确的业务类型参数

#### 供给数据监控页面
```tsx
// 修改前
<BrandTrendPopover
  brand={brand}
  dateRange={dateRange ? [dateRange[0].format('YYYY-MM-DD'), dateRange[1].format('YYYY-MM-DD')] : undefined}
>

// 修改后
<BrandTrendPopover
  brand={brand}
  dateRange={dateRange ? [dateRange[0].format('YYYY-MM-DD'), dateRange[1].format('YYYY-MM-DD')] : undefined}
  biz_name_lvl2={biz2 || undefined}  // 传递业务类型参数
>
```

#### 新老客数据监控页面
新老客数据监控页面使用固定的 `biz_name_lvl1 = '新老客数据'`，无需传递 `biz_name_lvl2` 参数。

## 🎯 修复效果

### 弹窗样式对比

#### 修复前（Table样式）
```
┌─────────────────────────────────┐
│ 字段        │ 值                │
├─────────────────────────────────┤
│ 品牌        │ 三养(美团)        │
│ 日期        │ 20250829         │
│ 业务类型     │ 供给数据          │
│ ...         │ ...              │
└─────────────────────────────────┘
```

#### 修复后（Descriptions样式）
```
┌─────────────────────────────────┐
│ 品牌: 三养          日期: 2025-08-29 │
│ 数据库状态: [存在]   数仓状态: [存在]  │
│ 数据库数据条数: 369  数仓数据条数: 369 │
│ 数据表名: heyin_sale_data.mt_... │
│ 数仓表名: dwp_prd.dws_mt_...     │
│ 数仓刷新时间: 每天凌晨02:00        │
│ 一级业务: 供给数据                │
│ 二级业务: 品牌商x城市              │
└─────────────────────────────────┘
```

### 折线图数据对比

#### 修复前
- API请求缺少 `biz_name_lvl2` 参数
- 返回的数据可能不准确或为空

#### 修复后
- API请求包含正确的 `biz_name_lvl2` 参数
- 返回准确的业务类型相关数据
- 折线图显示正确的趋势数据

## 📋 验证结果

### API测试
1. ✅ **供给数据监控API** - 正常返回28个品牌的数据
2. ✅ **新老客数据监控API** - 正常响应（当前无数据）
3. ✅ **品牌趋势API** - 正常返回15天的趋势数据

### 编译检查
1. ✅ **供给数据监控页面** - 无TypeScript错误
2. ✅ **新老客数据监控页面** - 无TypeScript错误
3. ✅ **组件导入** - 正确导入 `Descriptions` 和 `Tag` 组件

## 🎉 修复完成

现在供给数据监控和新老客数据监控页面的弹窗样式与其他页面完全一致，品牌名称点击后的折线图也能显示正确的数据。

### 统一的用户体验
- **一致的弹窗样式** - 所有监控页面使用相同的 `Descriptions` 组件
- **准确的数据展示** - 传递正确的业务类型参数获取准确数据
- **美观的视觉效果** - 使用 `Tag` 组件显示状态，更直观
- **完整的信息展示** - 包含所有必要的数据字段和业务信息

import React from 'react';
import './index.less';

interface ExcelFreezeTableProps {
  headers: string[];
  rows: Array<{
    brand: string;
    cells: React.ReactNode[];
  }>;
  formatHeader?: (header: string) => string;
  extractBrandName?: (brand: string) => string;
  renderBrandCell?: (brand: string, rowIndex: number) => React.ReactNode;
  className?: string;
}

const ExcelFreezeTable: React.FC<ExcelFreezeTableProps> = ({
  headers,
  rows,
  formatHeader = (header) => header,
  extractBrandName = (brand) => brand,
  renderBrandCell,
  className = ''
}) => {
  return (
    <div className={`excel-freeze-table ${className}`}>
      <table>
        <thead>
          <tr>
            <th className="freeze-corner">品牌</th>
            {headers.map((header, index) => (
              <th key={index} className="freeze-header">
                {formatHeader(header)}
              </th>
            ))}
          </tr>
        </thead>
        <tbody>
          {rows.map((row, rowIndex) => (
            <tr key={rowIndex}>
              <td className="freeze-brand">
                {renderBrandCell ? 
                  renderBrandCell(row.brand, rowIndex) : 
                  <span>{extractBrandName(row.brand)}</span>
                }
              </td>
              {row.cells.map((cell, cellIndex) => (
                <td key={cellIndex} className="data-cell">
                  {cell}
                </td>
              ))}
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
};

export default ExcelFreezeTable;

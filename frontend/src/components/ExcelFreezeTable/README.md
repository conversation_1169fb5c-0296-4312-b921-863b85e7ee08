# ExcelFreezeTable Excel风格冻结表格组件

一个完全模仿Excel冻结窗格效果的React表格组件，使用CSS `position: sticky` 实现真正的冻结首行和首列效果。

## 🎯 功能特点

- ✅ **Excel风格冻结** - 完全模仿Excel冻结窗格的行为
- ✅ **冻结首行** - 表头在垂直滚动时始终可见
- ✅ **冻结首列** - 品牌列在水平滚动时始终可见  
- ✅ **冻结左上角** - 左上角单元格在任意滚动时都保持固定
- ✅ **原生滚动** - 无需JavaScript，完全依靠CSS实现
- ✅ **性能优异** - 使用浏览器原生sticky定位，性能最佳
- ✅ **响应式设计** - 适配不同屏幕尺寸

## 🔧 技术实现

### 核心技术
- **CSS Sticky定位** - 使用 `position: sticky` 实现冻结效果
- **层级管理** - 通过 `z-index` 确保正确的显示层次
- **边框处理** - 使用 `box-shadow` 实现冻结区域的边框效果

### 关键CSS
```css
/* 冻结表头 */
thead {
  position: sticky;
  top: 0;
  z-index: 10;
}

/* 冻结首列 */
.freeze-brand {
  position: sticky;
  left: 0;
  z-index: 11;
}

/* 冻结左上角 */
.freeze-corner {
  position: sticky;
  top: 0;
  left: 0;
  z-index: 12;
}
```

## 📱 使用方法

### 基本用法
```tsx
import ExcelFreezeTable from '@/components/ExcelFreezeTable';

<ExcelFreezeTable
  headers={['08/15', '08/16', '08/17', '08/18']}
  rows={[
    {
      brand: '三养(美团)',
      cells: [
        <StatusIcon status={0} />,
        <StatusIcon status={1} />,
        <StatusIcon status={2} />,
        <StatusIcon status={3} />
      ]
    }
  ]}
  formatHeader={(header) => header}
  extractBrandName={(brand) => brand.split('(')[0]}
  renderBrandCell={(brand) => (
    <BrandTrendPopover brand={brand}>
      <span className="brand-name-hover">{brand}</span>
    </BrandTrendPopover>
  )}
/>
```

### 在监控页面中使用
```tsx
<ExcelFreezeTable
  headers={matrixData.dates}
  rows={matrixData.matrix.map((brandData) => ({
    brand: brandData.brand,
    cells: brandData.dates.map((dateData, dateIndex) => (
      <div
        className="status-icon-cell"
        onClick={() => handleCellClick(brandData, dateData, matrixData.dates[dateIndex])}
        title={getStatusText(dateData.status)}
      >
        {renderStatusIcon(dateData.status)}
      </div>
    ))
  }))}
  formatHeader={formatDate}
  extractBrandName={extractBrandName}
  renderBrandCell={(brand: string) => (
    <BrandTrendPopover brand={brand}>
      <span className="brand-name-hover">{extractBrandName(brand)}</span>
    </BrandTrendPopover>
  )}
/>
```

## 📋 API

### Props

| 参数 | 说明 | 类型 | 默认值 |
|------|------|------|--------|
| headers | 表头数据数组 | `string[]` | - |
| rows | 行数据数组 | `Array<{brand: string, cells: React.ReactNode[]}>` | - |
| formatHeader | 表头格式化函数 | `(header: string) => string` | `(header) => header` |
| extractBrandName | 品牌名提取函数 | `(brand: string) => string` | `(brand) => brand` |
| renderBrandCell | 品牌单元格自定义渲染 | `(brand: string, rowIndex: number) => React.ReactNode` | - |
| className | 自定义CSS类名 | `string` | `''` |

### 数据结构

#### headers
表头数据，通常是日期字符串数组：
```typescript
['20250815', '20250816', '20250817', ...]
```

#### rows
行数据，每行包含品牌名和单元格数据：
```typescript
[
  {
    brand: '三养(美团)',
    cells: [
      <StatusIcon status={0} onClick={...} />,
      <StatusIcon status={1} onClick={...} />,
      // ...
    ]
  }
]
```

## 🎨 样式定制

### 主要CSS类名
- `.excel-freeze-table` - 容器
- `.freeze-corner` - 左上角冻结单元格
- `.freeze-header` - 冻结表头单元格
- `.freeze-brand` - 冻结品牌列单元格
- `.data-cell` - 数据单元格
- `.brand-name-hover` - 品牌名悬停效果

### 自定义样式
```css
.excel-freeze-table {
  /* 自定义容器样式 */
}

.excel-freeze-table .freeze-corner {
  /* 自定义左上角样式 */
}

.excel-freeze-table .freeze-brand {
  /* 自定义品牌列样式 */
}
```

## 🔄 与Excel的对比

| 特性 | Excel冻结窗格 | ExcelFreezeTable |
|------|---------------|------------------|
| 冻结首行 | ✅ | ✅ |
| 冻结首列 | ✅ | ✅ |
| 冻结左上角 | ✅ | ✅ |
| 滚动体验 | 原生流畅 | 原生流畅 |
| 性能表现 | 优秀 | 优秀 |
| 视觉效果 | 无缝衔接 | 无缝衔接 |

## 🌐 浏览器兼容性

- **Chrome 56+** - 完全支持
- **Firefox 59+** - 完全支持  
- **Safari 13+** - 完全支持
- **Edge 16+** - 完全支持

> 注意：需要浏览器支持 `position: sticky` 特性

## 🚀 性能优势

1. **零JavaScript** - 冻结效果完全由CSS实现，无需监听滚动事件
2. **原生滚动** - 使用浏览器原生滚动，性能最佳
3. **GPU加速** - sticky定位由GPU处理，流畅度极高
4. **内存友好** - 无需额外的DOM操作和事件监听

## 📝 注意事项

1. **表格结构** - 必须使用标准的 `<table>` 结构
2. **边框处理** - 冻结区域的边框使用 `box-shadow` 实现
3. **层级管理** - 确保z-index层级正确：角落(12) > 品牌列(11) > 表头(10)
4. **滚动容器** - 表格容器必须设置固定高度和 `overflow: auto`

## 🎬 演示

查看 `demo.html` 文件可以看到完整的演示效果，包括：
- 30个品牌行
- 20个日期列  
- 完整的冻结效果
- 交互动画效果

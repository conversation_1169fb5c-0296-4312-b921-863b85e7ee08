.excel-freeze-table {
  width: 100%;
  max-height: none;
  overflow-x: auto;
  overflow-y: visible;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  background: #fff;
  position: relative;

  table {
    border-collapse: separate;
    border-spacing: 0;
    width: 100%;
    min-width: 100%;

    // 冻结首行（表头）
    thead {
      position: sticky;
      top: 0;
      z-index: 10;
      
      th {
        background: #fafafa;
        border-bottom: 1px solid #d9d9d9;
        border-right: 1px solid #d9d9d9;
        padding: 8px 12px;
        text-align: center;
        font-weight: 600;
        font-size: 14px;
        color: #262626;
        white-space: nowrap;
        min-width: 60px;
        height: 40px;
        box-sizing: border-box;

        &:last-child {
          border-right: none;
        }

        // 冻结左上角单元格
        &.freeze-corner {
          position: sticky;
          left: 0;
          z-index: 12;
          background: #fafafa;
          border-right: 1px solid #d9d9d9;
          width: 150px;
          min-width: 150px;
          max-width: 150px;
          text-align: center;
          box-shadow: 1px 0 0 0 #d9d9d9;
        }

        // 普通表头
        &.freeze-header {
          background: #fafafa;
          position: sticky;
          top: 0;
          z-index: 10;
        }
      }
    }

    tbody {
      tr {
        &:hover {
          background: rgba(24, 144, 255, 0.04);
        }

        td {
          border-bottom: 1px solid #f0f0f0;
          border-right: 1px solid #f0f0f0;
          padding: 8px 12px;
          background: #fff;
          height: 48px;
          box-sizing: border-box;
          vertical-align: middle;

          &:last-child {
            border-right: none;
          }

          // 冻结首列（品牌列）
          &.freeze-brand {
            position: sticky;
            left: 0;
            z-index: 11;
            background: #fff;
            border-right: 1px solid #d9d9d9;
            width: 150px;
            min-width: 150px;
            max-width: 150px;
            text-align: left;
            font-weight: 500;
            box-shadow: 1px 0 0 0 #d9d9d9;

            .brand-name-hover {
              cursor: pointer;
              transition: color 0.2s ease;
              
              &:hover {
                color: #1890ff;
                text-decoration: underline;
              }
            }

            // 行悬停时品牌列也要变色
            tr:hover & {
              background: rgba(24, 144, 255, 0.04) !important;
            }
          }

          // 数据单元格
          &.data-cell {
            text-align: center;
            cursor: pointer;
            transition: all 0.2s ease;
            min-width: 60px;

            &:hover {
              background: #f5f5f5;
              transform: scale(1.02);
            }

            .status-icon-cell {
              display: flex;
              align-items: center;
              justify-content: center;
              width: 100%;
              height: 100%;
              cursor: pointer;
              padding: 4px;
              transition: all 0.3s ease;

              &:hover {
                transform: scale(1.1);
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
              }
            }
          }
        }
      }
    }
  }

  // 滚动条样式
  &::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  &::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
  }

  &::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;

    &:hover {
      background: #a8a8a8;
    }
  }

  &::-webkit-scrollbar-corner {
    background: #f1f1f1;
  }
}

// 响应式调整
@media (max-width: 768px) {
  .excel-freeze-table {
    table {
      thead th.freeze-corner,
      tbody td.freeze-brand {
        width: 120px;
        min-width: 120px;
        max-width: 120px;
      }
    }
  }
}

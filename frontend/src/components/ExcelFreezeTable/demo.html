<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Excel风格冻结表格演示</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        
        .demo-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        h1 {
            text-align: center;
            color: #262626;
            margin-bottom: 30px;
        }
        
        .description {
            background: #f0f8ff;
            padding: 15px;
            border-radius: 6px;
            margin-bottom: 20px;
            border-left: 4px solid #1890ff;
        }
        
        /* Excel风格冻结表格样式 */
        .excel-freeze-table {
            width: 100%;
            max-height: none;
            overflow-x: auto;
            overflow-y: visible;
            border: 1px solid #d9d9d9;
            border-radius: 6px;
            background: #fff;
            position: relative;
        }

        .excel-freeze-table table {
            border-collapse: separate;
            border-spacing: 0;
            width: 100%;
            min-width: 100%;
        }

        /* 冻结首行（表头） */
        .excel-freeze-table thead {
            position: sticky;
            top: 0;
            z-index: 10;
        }

        .excel-freeze-table thead th {
            background: #fafafa;
            border-bottom: 1px solid #d9d9d9;
            border-right: 1px solid #d9d9d9;
            padding: 8px 12px;
            text-align: center;
            font-weight: 600;
            font-size: 14px;
            color: #262626;
            white-space: nowrap;
            min-width: 60px;
            height: 40px;
            box-sizing: border-box;
        }

        .excel-freeze-table thead th:last-child {
            border-right: none;
        }

        /* 冻结左上角单元格 */
        .excel-freeze-table thead th.freeze-corner {
            position: sticky;
            left: 0;
            z-index: 12;
            background: #fafafa;
            border-right: 1px solid #d9d9d9;
            width: 150px;
            min-width: 150px;
            max-width: 150px;
            text-align: center;
            box-shadow: 1px 0 0 0 #d9d9d9;
        }

        /* 普通表头 */
        .excel-freeze-table thead th.freeze-header {
            background: #fafafa;
            position: sticky;
            top: 0;
            z-index: 10;
        }

        .excel-freeze-table tbody tr:hover {
            background: rgba(24, 144, 255, 0.04);
        }

        .excel-freeze-table tbody td {
            border-bottom: 1px solid #f0f0f0;
            border-right: 1px solid #f0f0f0;
            padding: 8px 12px;
            background: #fff;
            height: 48px;
            box-sizing: border-box;
            vertical-align: middle;
        }

        .excel-freeze-table tbody td:last-child {
            border-right: none;
        }

        /* 冻结首列（品牌列） */
        .excel-freeze-table tbody td.freeze-brand {
            position: sticky;
            left: 0;
            z-index: 11;
            background: #fff;
            border-right: 1px solid #d9d9d9;
            width: 150px;
            min-width: 150px;
            max-width: 150px;
            text-align: left;
            font-weight: 500;
            box-shadow: 1px 0 0 0 #d9d9d9;
        }

        .excel-freeze-table tbody tr:hover td.freeze-brand {
            background: rgba(24, 144, 255, 0.04);
        }

        .brand-name-hover {
            cursor: pointer;
            transition: color 0.2s ease;
        }

        .brand-name-hover:hover {
            color: #1890ff;
            text-decoration: underline;
        }

        /* 数据单元格 */
        .excel-freeze-table tbody td.data-cell {
            text-align: center;
            cursor: pointer;
            transition: all 0.2s ease;
            min-width: 60px;
        }

        .excel-freeze-table tbody td.data-cell:hover {
            background: #f5f5f5;
            transform: scale(1.02);
        }

        .status-icon {
            width: 24px;
            height: 24px;
            border-radius: 50%;
            display: inline-block;
            transition: all 0.3s ease;
        }

        .status-icon:hover {
            transform: scale(1.1);
        }

        .status-0 { background: #ff4d4f; }
        .status-1 { background: #faad14; }
        .status-2 { background: #52c41a; }
        .status-3 { background: #1890ff; }

        /* 滚动条样式 */
        .excel-freeze-table::-webkit-scrollbar {
            width: 8px;
            height: 8px;
        }

        .excel-freeze-table::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 4px;
        }

        .excel-freeze-table::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 4px;
        }

        .excel-freeze-table::-webkit-scrollbar-thumb:hover {
            background: #a8a8a8;
        }

        .excel-freeze-table::-webkit-scrollbar-corner {
            background: #f1f1f1;
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <h1>Excel风格冻结表格演示</h1>
        
        <div class="description">
            <h3>功能特点：</h3>
            <ul>
                <li>✅ <strong>冻结首行</strong> - 所有表头单元格都使用 <code>position: sticky; top: 0</code> 实现固定</li>
                <li>✅ <strong>冻结首列</strong> - 使用 <code>position: sticky; left: 0</code> 实现品牌列固定</li>
                <li>✅ <strong>冻结左上角</strong> - 同时使用 <code>top: 0; left: 0</code> 实现角落单元格固定</li>
                <li>✅ <strong>全展开显示</strong> - 垂直方向全展开，无滚动条，所有行一次性显示</li>
                <li>✅ <strong>原生滚动</strong> - 无需JavaScript，完全依靠CSS实现</li>
                <li>✅ <strong>Excel体验</strong> - 与Excel冻结窗格效果完全一致</li>
            </ul>
            <p><strong>操作说明：</strong>尝试水平滚动表格，观察所有表头单元格（包括右上角）始终保持可见。垂直方向所有行全部展开显示。</p>
        </div>

        <div class="excel-freeze-table">
            <table>
                <thead>
                    <tr>
                        <th class="freeze-corner">品牌</th>
                        <th class="freeze-header">08/15</th>
                        <th class="freeze-header">08/16</th>
                        <th class="freeze-header">08/17</th>
                        <th class="freeze-header">08/18</th>
                        <th class="freeze-header">08/19</th>
                        <th class="freeze-header">08/20</th>
                        <th class="freeze-header">08/21</th>
                        <th class="freeze-header">08/22</th>
                        <th class="freeze-header">08/23</th>
                        <th class="freeze-header">08/24</th>
                        <th class="freeze-header">08/25</th>
                        <th class="freeze-header">08/26</th>
                        <th class="freeze-header">08/27</th>
                        <th class="freeze-header">08/28</th>
                        <th class="freeze-header">08/29</th>
                        <th class="freeze-header">08/30</th>
                        <th class="freeze-header">08/31</th>
                        <th class="freeze-header">09/01</th>
                        <th class="freeze-header">09/02</th>
                        <th class="freeze-header">09/03</th>
                    </tr>
                </thead>
                <tbody id="tableBody">
                    <!-- 动态生成内容 -->
                </tbody>
            </table>
        </div>
    </div>

    <script>
        // 生成演示数据
        const brands = [
            '三养', '三胖蛋', '上好佳', '丰科', '优诺', '佳沛', '佳能', '保乐力加', 
            '可口可乐', '吉得利', '嘉顿', '圣农', '太太乐', '好来', '水井坊', '海天',
            '百事食品', '科赴', '纳宝帝', '纽瑞优', '若羽臣', '迈大', '通用磨坊', 
            '金沙酒业', '雀巢', '雷米高', '额外品牌1', '额外品牌2', '额外品牌3', '额外品牌4'
        ];
        const dates = 20;
        
        function generateRandomStatus() {
            return Math.floor(Math.random() * 4);
        }
        
        function createStatusIcon(status) {
            return `<div class="status-icon status-${status}" title="状态${status}"></div>`;
        }
        
        // 生成表格内容
        const tableBody = document.getElementById('tableBody');
        brands.forEach(brand => {
            const row = document.createElement('tr');
            
            // 品牌列（冻结）
            const brandCell = document.createElement('td');
            brandCell.className = 'freeze-brand';
            brandCell.innerHTML = `<span class="brand-name-hover">${brand}</span>`;
            row.appendChild(brandCell);
            
            // 数据列
            for (let i = 0; i < dates; i++) {
                const cell = document.createElement('td');
                cell.className = 'data-cell';
                cell.innerHTML = createStatusIcon(generateRandomStatus());
                row.appendChild(cell);
            }
            
            tableBody.appendChild(row);
        });
        
        console.log('Excel风格冻结表格演示已加载完成！');
        console.log('表格包含', brands.length, '个品牌，', dates, '个日期列');
    </script>
</body>
</html>

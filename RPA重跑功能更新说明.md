# RPA重跑功能更新说明

## 功能概述

在RTB监控页面的数据状态矩阵中，点击状态标记弹窗中的"RPA重跑"按钮时，现在会根据二级业务类型调用不同的外部webhook接口，实现真正的RPA重跑功能。

## 🔧 核心逻辑

### 二级业务类型映射

| 二级业务类型 | Webhook URL |
|-------------|-------------|
| 综合投放 | `https://api.yingdao.com/api/tool/ipaas/webhook/callback/853591947531247616` |
| 投放广告 | `https://api.yingdao.com/api/tool/ipaas/webhook/callback/856829098951479296` |

### 请求流程

1. **参数验证**: 检查二级业务类型是否支持
2. **URL选择**: 根据二级业务类型选择对应的webhook URL
3. **数据准备**: 构建发送给webhook的数据包
4. **HTTP调用**: POST请求调用外部webhook
5. **结果处理**: 处理webhook响应并返回给前端

## 📋 API接口详情

### 接口信息
- **路径**: `/api/rerun_rpa`
- **方法**: POST
- **Content-Type**: application/json

### 请求参数
```json
{
  "brand": "品牌名称",
  "platform": "平台名称", 
  "date": "2025-08-25",
  "data_table_name": "数据表名",
  "biz_name_lvl2": "综合投放"  // 或 "投放广告"
}
```

### 响应格式

#### 成功响应
```json
{
  "code": 0,
  "msg": "RPA重跑请求已提交成功！品牌: xxx, 平台: xxx, 日期: xxx",
  "webhook_response": {
    // webhook返回的完整响应数据
  }
}
```

#### 错误响应
```json
{
  "code": 1,
  "msg": "错误信息"
}
```

## 🛡️ 错误处理

### 支持的错误类型

1. **参数验证错误**
   - 请求数据为空
   - 不支持的二级业务类型

2. **网络请求错误**
   - 连接超时（30秒）
   - 网络异常
   - HTTP状态码非200

3. **系统错误**
   - 其他未预期的异常

### 错误示例

```json
{
  "code": 1,
  "msg": "不支持的二级业务类型: 小时综合投放，仅支持'综合投放'和'投放广告'"
}
```

## 🔍 测试验证

### 测试用例1: 综合投放
```bash
curl -X POST "http://127.0.0.1:5001/api/rerun_rpa" \
  -H "Content-Type: application/json" \
  -d '{
    "brand": "测试品牌",
    "platform": "测试平台",
    "date": "2025-08-25",
    "data_table_name": "test_table",
    "biz_name_lvl2": "综合投放"
  }'
```

### 测试用例2: 投放广告
```bash
curl -X POST "http://127.0.0.1:5001/api/rerun_rpa" \
  -H "Content-Type: application/json" \
  -d '{
    "brand": "测试品牌2",
    "platform": "测试平台2", 
    "date": "2025-08-25",
    "data_table_name": "test_table2",
    "biz_name_lvl2": "投放广告"
  }'
```

### 测试用例3: 不支持的业务类型
```bash
curl -X POST "http://127.0.0.1:5001/api/rerun_rpa" \
  -H "Content-Type: application/json" \
  -d '{
    "brand": "测试品牌3",
    "platform": "测试平台3",
    "date": "2025-08-25", 
    "data_table_name": "test_table3",
    "biz_name_lvl2": "不支持的业务"
  }'
```

## 📊 实际测试结果

✅ **综合投放**: 成功调用webhook，返回完整响应数据
✅ **投放广告**: 成功调用webhook，返回完整响应数据  
✅ **错误处理**: 正确识别并拒绝不支持的业务类型

## 🔄 前端集成

前端无需修改，现有的"RPA重跑"按钮会自动使用新的逻辑：

1. 用户点击状态图标打开详情弹窗
2. 点击"RPA重跑"按钮
3. 前端发送POST请求到`/api/rerun_rpa`
4. 后端根据二级业务类型调用对应webhook
5. 返回执行结果给用户

## 🚀 功能优势

- **智能路由**: 根据业务类型自动选择正确的处理流程
- **完整日志**: 记录所有请求参数和响应结果
- **错误处理**: 全面的异常处理和用户友好的错误提示
- **超时控制**: 30秒超时避免长时间等待
- **响应透传**: 将webhook的完整响应返回给前端

## 📝 注意事项

1. 确保网络能够访问外部webhook URL
2. webhook调用有30秒超时限制
3. 只支持"综合投放"和"投放广告"两种二级业务类型
4. 所有请求和响应都会在后端日志中记录

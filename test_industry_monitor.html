<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>行业数据监控测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .filters {
            display: flex;
            gap: 20px;
            margin-bottom: 20px;
            flex-wrap: wrap;
            align-items: center;
        }
        .filter-group {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        select, input, button {
            padding: 8px 12px;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            font-size: 14px;
        }
        button {
            background-color: #1890ff;
            color: white;
            border: none;
            cursor: pointer;
        }
        button:hover {
            background-color: #40a9ff;
        }
        .stats {
            display: flex;
            gap: 24px;
            margin-bottom: 20px;
            font-size: 14px;
        }
        .stats span {
            color: #666;
        }
        .stats strong {
            color: #333;
        }
        .matrix-container {
            overflow: auto;
            max-height: 600px;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            font-size: 12px;
        }
        th, td {
            border: 1px solid #e8e8e8;
            padding: 8px;
            text-align: center;
        }
        th {
            background-color: #fafafa;
            position: sticky;
            top: 0;
            z-index: 10;
        }
        .brand-cell {
            background-color: #fafafa;
            position: sticky;
            left: 0;
            z-index: 5;
            text-align: left;
            min-width: 120px;
        }
        .status-cell {
            width: 40px;
            height: 30px;
            cursor: pointer;
        }
        .status-0 { background-color: #ff4d4f; } /* 红色 - 无数据 */
        .status-1 { background-color: #faad14; } /* 黄色 - 部分数据 */
        .status-2 { background-color: #52c41a; } /* 绿色 - 数据正常 */
        .status-3 { background-color: #1890ff; } /* 蓝色 - 数据不一致 */
        .loading {
            text-align: center;
            padding: 40px;
            color: #666;
        }
        .error {
            color: #ff4d4f;
            background-color: #fff2f0;
            border: 1px solid #ffccc7;
            padding: 12px;
            border-radius: 4px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>行业数据监控</h1>
        
        <div class="filters">
            <div class="filter-group">
                <label>二级业务:</label>
                <select id="biz2Select">
                    <option value="">加载中...</option>
                </select>
            </div>
            
            <div class="filter-group">
                <label>周期维度:</label>
                <select id="periodTypeSelect">
                    <option value="">加载中...</option>
                </select>
            </div>
            
            <div class="filter-group">
                <label>开始日期:</label>
                <input type="text" id="startDate" placeholder="2024-01">
            </div>
            
            <div class="filter-group">
                <label>结束日期:</label>
                <input type="text" id="endDate" placeholder="2024-03">
            </div>
            
            <div class="filter-group">
                <label>品牌筛选:</label>
                <input type="text" id="brandFilter" placeholder="输入品牌名称">
            </div>
            
            <button onclick="loadData()">查询</button>
        </div>
        
        <div id="error" class="error" style="display: none;"></div>
        
        <div id="stats" class="stats" style="display: none;">
            <span>品牌数量: <strong id="totalBrands">0</strong></span>
            <span>异常数量: <strong id="anomalyCount" style="color: #ff4d4f;">0</strong></span>
            <span>完成率: <strong id="completionRate" style="color: #52c41a;">0%</strong></span>
        </div>
        
        <div id="loading" class="loading">加载中...</div>
        
        <div id="matrixContainer" class="matrix-container" style="display: none;">
            <table id="matrixTable">
                <thead>
                    <tr id="headerRow">
                        <th class="brand-cell">品牌</th>
                    </tr>
                </thead>
                <tbody id="matrixBody">
                </tbody>
            </table>
        </div>
    </div>

    <script>
        let businessOptions = {};
        
        // 加载业务选项
        async function loadBusinessOptions() {
            try {
                const response = await fetch('http://127.0.0.1:5001/api/business_options?biz_name_lvl1=行业数据');
                const data = await response.json();
                
                if (data.code === 0) {
                    businessOptions = data.data;
                    
                    // 填充二级业务选项
                    const biz2Select = document.getElementById('biz2Select');
                    biz2Select.innerHTML = '<option value="">请选择</option>';
                    businessOptions.biz_lvl2_options.forEach(option => {
                        const optionElement = document.createElement('option');
                        optionElement.value = option;
                        optionElement.textContent = option;
                        biz2Select.appendChild(optionElement);
                    });
                    
                    // 填充周期维度选项
                    const periodTypeSelect = document.getElementById('periodTypeSelect');
                    periodTypeSelect.innerHTML = '<option value="">请选择</option>';
                    businessOptions.period_type_options.forEach(option => {
                        const optionElement = document.createElement('option');
                        optionElement.value = option;
                        optionElement.textContent = option;
                        periodTypeSelect.appendChild(optionElement);
                    });
                    
                    // 设置默认值
                    if (businessOptions.biz_lvl2_options.length > 0) {
                        biz2Select.value = businessOptions.biz_lvl2_options[0];
                    }
                    if (businessOptions.period_type_options.includes('月')) {
                        periodTypeSelect.value = '月';
                    }
                    
                    // 设置默认日期
                    document.getElementById('startDate').value = '2024-01';
                    document.getElementById('endDate').value = '2024-03';
                    
                    // 自动加载数据
                    loadData();
                } else {
                    showError('加载业务选项失败: ' + data.msg);
                }
            } catch (error) {
                showError('加载业务选项失败: ' + error.message);
            }
        }
        
        // 加载监控数据
        async function loadData() {
            const biz2 = document.getElementById('biz2Select').value;
            const periodType = document.getElementById('periodTypeSelect').value;
            const startDate = document.getElementById('startDate').value;
            const endDate = document.getElementById('endDate').value;
            const brandFilter = document.getElementById('brandFilter').value;
            
            if (!biz2 || !periodType) {
                showError('请选择二级业务和周期维度');
                return;
            }
            
            showLoading();
            hideError();
            
            try {
                const params = new URLSearchParams({
                    biz_name_lvl1: '行业数据',
                    biz_name_lvl2: biz2,
                    period_type: periodType
                });
                
                if (startDate) params.append('start_date', startDate);
                if (endDate) params.append('end_date', endDate);
                
                const response = await fetch('http://127.0.0.1:5001/api/monitor?' + params.toString());
                const data = await response.json();
                
                if (data.code === 0) {
                    renderMatrix(data.data, brandFilter);
                } else {
                    showError('加载数据失败: ' + data.msg);
                }
            } catch (error) {
                showError('加载数据失败: ' + error.message);
            }
        }
        
        // 渲染数据矩阵
        function renderMatrix(data, brandFilter) {
            hideLoading();
            
            // 过滤品牌
            let filteredMatrix = data.matrix;
            if (brandFilter && brandFilter.trim()) {
                filteredMatrix = data.matrix.filter(brandData => {
                    const brandName = brandData.brand.split('(')[0].toLowerCase();
                    return brandName.includes(brandFilter.toLowerCase());
                });
            }
            
            // 更新统计信息
            document.getElementById('totalBrands').textContent = filteredMatrix.length;
            document.getElementById('anomalyCount').textContent = data.stats.anomaly_count;
            document.getElementById('completionRate').textContent = data.stats.today_completion_rate;
            document.getElementById('stats').style.display = 'flex';
            
            // 渲染表头
            const headerRow = document.getElementById('headerRow');
            headerRow.innerHTML = '<th class="brand-cell">品牌</th>';
            data.dates.forEach(date => {
                const th = document.createElement('th');
                th.textContent = formatDate(date);
                headerRow.appendChild(th);
            });
            
            // 渲染表体
            const matrixBody = document.getElementById('matrixBody');
            matrixBody.innerHTML = '';
            
            filteredMatrix.forEach(brandData => {
                const row = document.createElement('tr');
                
                // 品牌列
                const brandCell = document.createElement('td');
                brandCell.className = 'brand-cell';
                brandCell.textContent = brandData.brand.split('(')[0]; // 只显示品牌名，不显示平台
                row.appendChild(brandCell);
                
                // 状态列
                brandData.dates.forEach((dateData, index) => {
                    const cell = document.createElement('td');
                    cell.className = `status-cell status-${dateData.status}`;
                    cell.title = getStatusText(dateData.status) + 
                        `\nDB: ${dateData.db_data_count || 0}, DW: ${dateData.dw_data_count || 0}`;
                    cell.onclick = () => showDetail(brandData.brand, data.dates[index], dateData);
                    row.appendChild(cell);
                });
                
                matrixBody.appendChild(row);
            });
            
            document.getElementById('matrixContainer').style.display = 'block';
        }
        
        // 格式化日期显示
        function formatDate(date) {
            if (date.length === 7 && date.includes('-')) {
                return date; // YYYY-MM
            } else if (date.length === 6) {
                return date.substring(0, 4) + '-' + date.substring(4, 6); // YYYYMM -> YYYY-MM
            }
            return date;
        }
        
        // 获取状态文本
        function getStatusText(status) {
            switch (status) {
                case 0: return '无数据';
                case 1: return '部分数据';
                case 2: return '数据正常';
                case 3: return '数据不一致';
                default: return '未知状态';
            }
        }
        
        // 显示详情
        function showDetail(brand, date, dateData) {
            alert(`品牌: ${brand}\n日期: ${date}\n状态: ${getStatusText(dateData.status)}\nDB数据量: ${dateData.db_data_count || 0}\nDW数据量: ${dateData.dw_data_count || 0}`);
        }
        
        // 显示错误
        function showError(message) {
            document.getElementById('error').textContent = message;
            document.getElementById('error').style.display = 'block';
            hideLoading();
        }
        
        // 隐藏错误
        function hideError() {
            document.getElementById('error').style.display = 'none';
        }
        
        // 显示加载中
        function showLoading() {
            document.getElementById('loading').style.display = 'block';
            document.getElementById('matrixContainer').style.display = 'none';
            document.getElementById('stats').style.display = 'none';
        }
        
        // 隐藏加载中
        function hideLoading() {
            document.getElementById('loading').style.display = 'none';
        }
        
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadBusinessOptions();
        });
    </script>
</body>
</html>

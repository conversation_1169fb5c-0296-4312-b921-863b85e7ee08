<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>默认日期范围测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            line-height: 1.6;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .success {
            color: #52c41a;
            font-weight: bold;
        }
        .info {
            color: #1890ff;
        }
        .date-range {
            background: #f0f0f0;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <h1>RTB监控页面默认日期范围测试</h1>
    
    <div class="test-section">
        <h3>📅 默认日期范围计算</h3>
        <div id="dateCalculation"></div>
    </div>
    
    <div class="test-section">
        <h3>🔗 API测试</h3>
        <button onclick="testAPI()">测试API默认日期范围</button>
        <div id="apiResult"></div>
    </div>
    
    <div class="test-section">
        <h3>📊 前端日期选择器模拟</h3>
        <div id="frontendSimulation"></div>
    </div>

    <script>
        // 模拟dayjs的基本功能
        function formatDate(date) {
            return date.toISOString().split('T')[0];
        }
        
        function subtractDays(date, days) {
            const result = new Date(date);
            result.setDate(result.getDate() - days);
            return result;
        }
        
        // 计算默认日期范围
        function calculateDefaultDateRange() {
            const today = new Date();
            const yesterday = subtractDays(today, 1); // T-1日
            const startDate = subtractDays(yesterday, 14); // 往前推14天，总共15天
            
            return {
                start: formatDate(startDate),
                end: formatDate(yesterday),
                days: 15
            };
        }
        
        // 显示日期计算结果
        function displayDateCalculation() {
            const range = calculateDefaultDateRange();
            const today = formatDate(new Date());
            
            document.getElementById('dateCalculation').innerHTML = `
                <div class="info">当前日期: ${today}</div>
                <div class="date-range">
                    <strong>默认日期范围:</strong><br>
                    开始日期: ${range.start}<br>
                    结束日期: ${range.end}<br>
                    总天数: ${range.days}天
                </div>
                <div class="success">✅ 日期范围计算正确</div>
            `;
        }
        
        // 测试API
        async function testAPI() {
            const resultDiv = document.getElementById('apiResult');
            resultDiv.innerHTML = '<div class="info">🔄 正在测试API...</div>';
            
            try {
                const response = await fetch('http://127.0.0.1:5006/api/brand_trend?brand=test');
                const data = await response.json();
                
                if (data.code === 0) {
                    const dates = data.data.dates;
                    const expectedRange = calculateDefaultDateRange();
                    
                    const isCorrect = dates.length === 15 && 
                                    dates[0] === expectedRange.start && 
                                    dates[dates.length - 1] === expectedRange.end;
                    
                    resultDiv.innerHTML = `
                        <div class="date-range">
                            <strong>API返回的日期范围:</strong><br>
                            开始日期: ${dates[0]}<br>
                            结束日期: ${dates[dates.length - 1]}<br>
                            总天数: ${dates.length}天
                        </div>
                        <div class="${isCorrect ? 'success' : 'error'}">
                            ${isCorrect ? '✅ API默认日期范围正确' : '❌ API默认日期范围不正确'}
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `<div class="error">❌ API调用失败: ${data.msg}</div>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">❌ 网络错误: ${error.message}</div>`;
            }
        }
        
        // 模拟前端日期选择器
        function simulateFrontendDatePicker() {
            const range = calculateDefaultDateRange();
            
            document.getElementById('frontendSimulation').innerHTML = `
                <div class="info">模拟前端页面加载时的默认日期选择:</div>
                <div class="date-range">
                    <strong>日期选择器默认值:</strong><br>
                    开始日期: ${range.start}<br>
                    结束日期: ${range.end}
                </div>
                <div class="success">✅ 前端默认日期范围设置正确</div>
                <div style="margin-top: 10px; font-size: 12px; color: #666;">
                    用户打开RTB监控页面时，日期筛选器会自动选择这个范围
                </div>
            `;
        }
        
        // 页面加载时执行测试
        window.onload = function() {
            displayDateCalculation();
            simulateFrontendDatePicker();
        };
    </script>
</body>
</html>

#!/usr/bin/env python3
import pymysql

def debug_brand_trend():
    """调试品牌趋势数据差异问题"""
    
    # 数据库连接配置
    config = {
        'host': '127.0.0.1',
        'port': 3306,
        'user': 'root',
        'password': '123456',
        'database': 'rpa_warning',
        'charset': 'utf8mb4',
        'cursorclass': pymysql.cursors.DictCursor
    }
    
    try:
        conn = pymysql.connect(**config)
        with conn.cursor() as cursor:
            print("=== 调试品牌趋势数据差异 ===")
            print()
            
            # 查询三养品牌在美团平台的所有数据
            sql1 = """
            SELECT brand, platform, biz_name_lvl1, biz_name_lvl2, date, 
                   db_data_count, dw_data_count, data_type, period_type
            FROM rpa_data_monitor 
            WHERE brand = '三养' AND platform = '美团' AND date = '20250829'
            ORDER BY biz_name_lvl1, biz_name_lvl2
            """
            
            cursor.execute(sql1)
            all_records = cursor.fetchall()
            
            print(f"1. 三养(美团) 2025-08-29 的所有记录 ({len(all_records)}条):")
            print("-" * 100)
            for i, record in enumerate(all_records, 1):
                print(f"{i:2d}. {record['biz_name_lvl1']:8s} | {record['biz_name_lvl2'] or 'NULL':15s} | "
                      f"DB:{record['db_data_count']:4d} | DW:{record['dw_data_count']:4d} | "
                      f"{record['data_type'] or 'NULL':4s} | {record['period_type'] or 'NULL':2s}")
            print()
            
            # 模拟brand_trend API的查询（供给数据 + 品牌商x城市）
            sql2 = """
            SELECT DATE_FORMAT(date, '%Y-%m-%d') as date,
                   SUM(db_data_count) as total_db_count,
                   SUM(dw_data_count) as total_dw_count,
                   COUNT(*) as record_count
            FROM rpa_data_monitor 
            WHERE brand = '三养' AND platform = '美团' 
                  AND biz_name_lvl2 = '品牌商x城市'
                  AND date = '20250829'
            GROUP BY date
            """
            
            cursor.execute(sql2)
            trend_result = cursor.fetchone()
            
            print("2. brand_trend API查询结果 (biz_name_lvl2='品牌商x城市'):")
            print("-" * 60)
            if trend_result:
                print(f"   日期: {trend_result['date']}")
                print(f"   DB总数: {trend_result['total_db_count']}")
                print(f"   DW总数: {trend_result['total_dw_count']}")
                print(f"   记录数: {trend_result['record_count']}")
            else:
                print("   无匹配记录")
            print()
            
            # 模拟monitor API的查询（供给数据）
            sql3 = """
            SELECT biz_name_lvl1, biz_name_lvl2, db_data_count, dw_data_count
            FROM rpa_data_monitor 
            WHERE brand = '三养' AND platform = '美团' 
                  AND biz_name_lvl1 = '供给数据'
                  AND date = '20250829'
            ORDER BY biz_name_lvl2
            """
            
            cursor.execute(sql3)
            monitor_results = cursor.fetchall()
            
            print("3. monitor API查询结果 (biz_name_lvl1='供给数据'):")
            print("-" * 60)
            for i, record in enumerate(monitor_results, 1):
                print(f"   {i}. {record['biz_name_lvl2'] or 'NULL':15s} | "
                      f"DB:{record['db_data_count']:4d} | DW:{record['dw_data_count']:4d}")
            print()
            
            # 检查是否有重复或多条记录
            sql4 = """
            SELECT biz_name_lvl1, biz_name_lvl2, COUNT(*) as count,
                   SUM(db_data_count) as total_db, SUM(dw_data_count) as total_dw
            FROM rpa_data_monitor 
            WHERE brand = '三养' AND platform = '美团' AND date = '20250829'
            GROUP BY biz_name_lvl1, biz_name_lvl2
            HAVING COUNT(*) > 1
            """
            
            cursor.execute(sql4)
            duplicates = cursor.fetchall()
            
            print("4. 重复记录检查:")
            print("-" * 60)
            if duplicates:
                for dup in duplicates:
                    print(f"   {dup['biz_name_lvl1']} | {dup['biz_name_lvl2']} | 重复{dup['count']}条")
            else:
                print("   无重复记录")
            print()
            
            # 分析数据差异原因
            print("5. 数据差异分析:")
            print("-" * 60)
            
            # 计算供给数据的总和
            supply_total_db = sum(r['db_data_count'] for r in monitor_results)
            supply_total_dw = sum(r['dw_data_count'] for r in monitor_results)
            
            print(f"   供给数据总和: DB={supply_total_db}, DW={supply_total_dw}")
            
            if trend_result:
                print(f"   趋势数据总和: DB={trend_result['total_db_count']}, DW={trend_result['total_dw_count']}")
                print(f"   差异: DB={trend_result['total_db_count'] - supply_total_db}, "
                      f"DW={trend_result['total_dw_count'] - supply_total_dw}")
            
    except Exception as e:
        print(f"错误: {e}")
    finally:
        try:
            conn.close()
        except:
            pass

if __name__ == "__main__":
    debug_brand_trend()

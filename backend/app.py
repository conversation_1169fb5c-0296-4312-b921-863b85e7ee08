from flask import Flask, jsonify, request
from flask_cors import CORS
from collections import defaultdict
import requests
from datetime import datetime, timedelta
import calendar

from database import get_connection


app = Flask(__name__)
CORS(app)


def generate_date_range(start_date, end_date, period_type):
    """根据开始日期、结束日期和周期类型生成完整的日期列表"""
    if not start_date or not end_date:
        return []

    dates = []

    if period_type == '月':
        # 月维度：生成YYYY-MM格式的日期列表
        if len(start_date) == 10:  # YYYY-MM-DD格式
            start_year, start_month = int(start_date[:4]), int(start_date[5:7])
        elif len(start_date) == 7:  # YYYY-MM格式
            start_year, start_month = int(start_date[:4]), int(start_date[5:7])
        else:
            return []

        if len(end_date) == 10:  # YYYY-MM-DD格式
            end_year, end_month = int(end_date[:4]), int(end_date[5:7])
        elif len(end_date) == 7:  # YYYY-MM格式
            end_year, end_month = int(end_date[:4]), int(end_date[5:7])
        else:
            return []

        current_year, current_month = start_year, start_month
        while (current_year < end_year) or (current_year == end_year and current_month <= end_month):
            dates.append(f"{current_year:04d}-{current_month:02d}")
            current_month += 1
            if current_month > 12:
                current_month = 1
                current_year += 1
    else:
        # 日维度：生成YYYYMMDD格式的日期列表
        if len(start_date) == 10:  # YYYY-MM-DD格式
            start_dt = datetime.strptime(start_date, '%Y-%m-%d')
        else:
            return []

        if len(end_date) == 10:  # YYYY-MM-DD格式
            end_dt = datetime.strptime(end_date, '%Y-%m-%d')
        else:
            return []

        current_dt = start_dt
        while current_dt <= end_dt:
            dates.append(current_dt.strftime('%Y%m%d'))
            current_dt += timedelta(days=1)

    return dates


def transform_to_matrix(rows, start_date=None, end_date=None, period_type=None):
    """将行数据转换为矩阵格式"""
    # 生成完整的日期范围
    if start_date and end_date and period_type:
        complete_dates = generate_date_range(start_date, end_date, period_type)
    else:
        complete_dates = []

    # 如果没有数据但有日期范围，返回空的矩阵但包含完整日期
    if not rows:
        if complete_dates:
            return {"brands": [], "dates": complete_dates, "matrix": [], "stats": {"total_brands": 0, "total_platforms": 0, "anomaly_count": 0, "today_completion_rate": "0%"}}
        else:
            return {"brands": [], "dates": [], "matrix": [], "stats": {"total_brands": 0, "total_platforms": 0, "anomaly_count": 0, "today_completion_rate": "0%"}}

    # 按品牌+平台分组数据
    brand_data = defaultdict(lambda: defaultdict(dict))
    all_dates = set()
    platforms = set()

    for row in rows:
        brand_key = f"{row['brand']}({row['platform']})"
        date = row['date']
        platforms.add(row['platform'])
        all_dates.add(date)

        # 计算状态：0=红色(都没有), 1=黄色(部分有), 2=绿色(都有且相等), 3=蓝色(都有但不相等)
        db_exists = bool(row['db_exists'])
        dw_exists = bool(row['dw_exists'])
        db_count = row['db_data_count'] or 0
        dw_count = row['dw_data_count'] or 0

        if db_exists and dw_exists:
            # 都有数据，检查数据量是否相等
            if db_count > 0 and dw_count > 0 and db_count != dw_count:
                status = 3  # 蓝色 - 都有数据但数量不相等
            else:
                status = 2  # 绿色 - 都有数据且数量相等（或其中一个为0）
        elif db_exists or dw_exists:
            status = 1  # 黄色 - 部分有数据
        else:
            status = 0  # 红色 - 都没有数据

        brand_data[brand_key][date] = {
            "status": status,
            "db_exists": db_exists,
            "dw_exists": dw_exists,
            "db_data_count": row['db_data_count'],
            "dw_data_count": row['dw_data_count'],
            "data_table_name": row.get('data_table_name'),
            "dw_table_name": row.get('dw_table_name'),
            "dw_refresh_time": row.get('dw_refresh_time'),
            "biz_name_lvl1": row['biz_name_lvl1'],
            "biz_name_lvl2": row['biz_name_lvl2'],
            "raw_data": row
        }

    # 使用完整的日期范围，如果没有提供则使用数据中的日期
    if complete_dates:
        sorted_dates = complete_dates
    else:
        sorted_dates = sorted(list(all_dates))

    sorted_brands = sorted(list(brand_data.keys()))

    # 构建矩阵
    matrix = []
    anomaly_count = 0
    green_count = 0  # 绿色状态(成功)计数
    total_cells = 0

    for brand in sorted_brands:
        brand_row = {"brand": brand, "dates": []}
        for date in sorted_dates:
            if date in brand_data[brand]:
                cell_data = brand_data[brand][date]
                # 红色(0)和蓝色(3)都算作需要关注的状态
                if cell_data["status"] in [0, 3]:
                    anomaly_count += 1
                elif cell_data["status"] == 2:  # 绿色状态表示任务成功
                    green_count += 1
                total_cells += 1
            else:
                cell_data = {
                    "status": 0,
                    "db_exists": False,
                    "dw_exists": False,
                    "db_data_count": 0,
                    "dw_data_count": 0,
                    "data_table_name": None,
                    "dw_table_name": None,
                    "dw_refresh_time": None,
                    "biz_name_lvl1": None,
                    "biz_name_lvl2": None,
                    "raw_data": None
                }
                anomaly_count += 1
                total_cells += 1

            brand_row["dates"].append(cell_data)
        matrix.append(brand_row)

    # 计算任务成功率：绿色状态数量/总数量
    completion_rate = (green_count / total_cells * 100) if total_cells > 0 else 0

    stats = {
        "total_brands": len(sorted_brands),
        "total_platforms": len(platforms),
        "anomaly_count": anomaly_count,
        "today_completion_rate": f"{completion_rate:.0f}%"
    }

    return {
        "brands": sorted_brands,
        "dates": sorted_dates,
        "matrix": matrix,
        "stats": stats
    }


@app.route("/health", methods=["GET"])
def health():
    return jsonify({"status": "ok"})


@app.route("/api/debug", methods=["GET"])
def debug_data():
    """调试API：查看数据库中的实际数据"""
    try:
        conn = get_connection()
        with conn.cursor() as cursor:
            # 测试供给数据+月维度的查询
            cursor.execute("""
                SELECT COUNT(*) as count
                FROM rpa_data_monitor
                WHERE biz_name_lvl1 = '供给数据' AND period_type = '月'
            """)
            supply_month_count = cursor.fetchone()['count']

            # 查看供给数据+月维度的日期范围
            cursor.execute("""
                SELECT MIN(date) as min_date, MAX(date) as max_date
                FROM rpa_data_monitor
                WHERE biz_name_lvl1 = '供给数据' AND period_type = '月'
            """)
            date_range = cursor.fetchone()

            # 测试带日期范围的查询
            cursor.execute("""
                SELECT COUNT(*) as count
                FROM rpa_data_monitor
                WHERE biz_name_lvl1 = '供给数据' AND period_type = '月'
                AND date >= '2024-01' AND date <= '2025-08'
            """)
            filtered_count = cursor.fetchone()['count']

            result = {
                'supply_month_total': supply_month_count,
                'date_range': date_range,
                'filtered_count': filtered_count
            }

        return jsonify({"code": 0, "msg": "success", "data": result})
    except Exception as e:
        import traceback
        error_msg = f"Error: {str(e)}, Traceback: {traceback.format_exc()}"
        return jsonify({"code": -1, "msg": error_msg, "data": []})


@app.route("/api/monitor_simple", methods=["GET"])
def get_monitor_simple():
    """简化版监控API：只返回基本统计信息"""
    try:
        biz_name_lvl1 = request.args.get("biz_name_lvl1")
        period_type = request.args.get("period_type")
        start_date = request.args.get("start_date")
        end_date = request.args.get("end_date")

        where_clauses = []
        params = []

        if biz_name_lvl1:
            where_clauses.append("biz_name_lvl1 = %s")
            params.append(biz_name_lvl1)
        if period_type:
            where_clauses.append("period_type = %s")
            params.append(period_type)
        if start_date:
            where_clauses.append("date >= %s")
            params.append(start_date)
        if end_date:
            where_clauses.append("date <= %s")
            params.append(end_date)

        where_sql = f"WHERE {' AND '.join(where_clauses)}" if where_clauses else ""

        conn = get_connection()
        with conn.cursor() as cursor:
            # 获取基本统计信息
            cursor.execute(f"""
                SELECT
                    COUNT(DISTINCT CONCAT(brand, '(', platform, ')')) as brand_count,
                    COUNT(DISTINCT date) as date_count,
                    COUNT(*) as total_records
                FROM rpa_data_monitor
                {where_sql}
            """, params)
            stats = cursor.fetchone()

            # 获取日期列表
            cursor.execute(f"""
                SELECT DISTINCT date
                FROM rpa_data_monitor
                {where_sql}
                ORDER BY date
                LIMIT 100
            """, params)
            dates = [row['date'] for row in cursor.fetchall()]

            # 获取品牌列表
            cursor.execute(f"""
                SELECT DISTINCT CONCAT(brand, '(', platform, ')') as brand_name
                FROM rpa_data_monitor
                {where_sql}
                ORDER BY brand_name
                LIMIT 100
            """, params)
            brands = [row['brand_name'] for row in cursor.fetchall()]

        result = {
            "brands": brands,
            "dates": dates,
            "stats": {
                "total_brands": stats['brand_count'],
                "total_dates": stats['date_count'],
                "total_records": stats['total_records']
            }
        }

        return jsonify({"code": 0, "msg": "success", "data": result})
    except Exception as e:
        import traceback
        error_msg = f"Error: {str(e)}, Traceback: {traceback.format_exc()}"
        return jsonify({"code": -1, "msg": error_msg, "data": {}})


@app.route("/api/monitor", methods=["GET"])
def get_monitor_records():
    """查询 rpa_data_monitor 表。
    """
    params = []
    where_clauses = []

    biz_name_lvl1 = request.args.get("biz_name_lvl1")
    biz_name_lvl2 = request.args.get("biz_name_lvl2")
    platform = request.args.get("platform")
    brand = request.args.get("brand")
    date_str = request.args.get("date")
    start_date = request.args.get("start_date")
    end_date = request.args.get("end_date")
    period_type = request.args.get("period_type")
    data_type = request.args.get("data_type")

    if biz_name_lvl1:
        where_clauses.append("biz_name_lvl1 = %s")
        params.append(biz_name_lvl1)
    if biz_name_lvl2:
        where_clauses.append("biz_name_lvl2 = %s")
        params.append(biz_name_lvl2)
    if platform:
        where_clauses.append("platform = %s")
        params.append(platform)
    if brand:
        where_clauses.append("brand = %s")
        params.append(brand)
    if date_str:
        where_clauses.append("date = %s")
        params.append(date_str)
    if start_date:
        # 根据日期格式判断是日维度还是月维度
        if len(start_date) == 7 and start_date[4] == '-':
            # YYYY-MM 格式（月维度）
            # 数据库中的date字段可能是YYYYMM格式，需要转换
            start_date_db = start_date.replace('-', '')  # 转换为YYYYMM格式
            where_clauses.append("(date >= %s OR date >= %s)")
            params.extend([start_date, start_date_db])
        elif len(start_date) == 10 and start_date[4] == '-':
            # YYYY-MM-DD 格式（日维度）
            start_date_db = start_date.replace('-', '')  # 转换为YYYYMMDD格式
            where_clauses.append("(date >= %s OR date >= %s)")
            params.extend([start_date, start_date_db])
        else:
            # 其他格式直接使用
            where_clauses.append("date >= %s")
            params.append(start_date)
    if end_date:
        # 根据日期格式判断是日维度还是月维度
        if len(end_date) == 7 and end_date[4] == '-':
            # YYYY-MM 格式（月维度）
            # 数据库中的date字段可能是YYYYMM格式，需要转换
            end_date_db = end_date.replace('-', '')  # 转换为YYYYMM格式
            where_clauses.append("(date <= %s OR date <= %s)")
            params.extend([end_date, end_date_db])
        elif len(end_date) == 10 and end_date[4] == '-':
            # YYYY-MM-DD 格式（日维度）
            end_date_db = end_date.replace('-', '')  # 转换为YYYYMMDD格式
            where_clauses.append("(date <= %s OR date <= %s)")
            params.extend([end_date, end_date_db])
        else:
            # 其他格式直接使用
            where_clauses.append("date <= %s")
            params.append(end_date)
    if period_type:
        where_clauses.append("period_type = %s")
        params.append(period_type)
    if data_type:
        where_clauses.append("data_type = %s")
        params.append(data_type)

    where_sql = f"WHERE {' AND '.join(where_clauses)}" if where_clauses else ""

    # 根据period_type决定日期格式
    if period_type == '月':
        # 月维度：如果date字段已经是YYYY-MM格式，直接使用；如果是YYYYMM格式，转换为YYYY-MM
        date_select = """
        CASE
            WHEN date LIKE '____-__' THEN date
            WHEN LENGTH(date) = 6 AND date REGEXP '^[0-9]{6}$' THEN CONCAT(SUBSTRING(date, 1, 4), '-', SUBSTRING(date, 5, 2))
            WHEN LENGTH(date) = 8 AND date REGEXP '^[0-9]{8}$' THEN CONCAT(SUBSTRING(date, 1, 4), '-', SUBSTRING(date, 5, 2))
            ELSE date
        END as date"""
    else:
        # 日维度：格式化为YYYYMMDD
        date_select = "DATE_FORMAT(date, '%%Y%%m%%d') as date"

    # 不添加LIMIT限制，保持原有性能
    limit_clause = ""

    sql = (
        f"SELECT id, biz_name_lvl1, biz_name_lvl2, {date_select}, platform, brand, "
        "data_table_name, db_exists, dw_table_name, dw_exists, dw_data_count, "
        "db_data_count, dw_refresh_time, period_type, data_type "
        "FROM rpa_data_monitor "
        f"{where_sql} "
        "ORDER BY date DESC, id DESC "
        f"{limit_clause}"
    )

    try:
        conn = get_connection()
        with conn.cursor() as cursor:
            cursor.execute(sql, params)
            rows = cursor.fetchall()

            # 转换数据为矩阵格式，传递日期范围参数
            matrix_data = transform_to_matrix(rows, start_date, end_date, period_type)

        return jsonify({"code": 0, "msg": "success", "data": matrix_data})
    except Exception as e:
        return jsonify({"code": 1, "msg": str(e)}), 500
    finally:
        try:
            conn.close()
        except Exception:
            pass


@app.route("/api/rerun_rpa", methods=["POST"])
def rerun_rpa():
    """RPA重跑接口"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({"code": 1, "msg": "请求数据不能为空"}), 400

        brand = data.get('brand', '')
        platform = data.get('platform', '')
        date = data.get('date', '')
        table_name = data.get('data_table_name', '')
        biz_name_lvl2 = data.get('biz_name_lvl2', '')

        print(f"RPA重跑请求: 品牌={brand}, 平台={platform}, 日期={date}, 表名={table_name}, 二级业务={biz_name_lvl2}")

        # 根据二级业务类型选择不同的webhook URL
        webhook_url = None
        if biz_name_lvl2 == "综合投放":
            webhook_url = "https://api.yingdao.com/api/tool/ipaas/webhook/callback/853591947531247616"
        elif biz_name_lvl2 == "投放广告":
            webhook_url = "https://api.yingdao.com/api/tool/ipaas/webhook/callback/856829098951479296"
        else:
            return jsonify({
                "code": 1,
                "msg": f"不支持的二级业务类型: {biz_name_lvl2}，仅支持'综合投放'和'投放广告'"
            }), 400

        # 准备发送给webhook的数据
        webhook_data = {
            "brand": brand,
            "platform": platform,
            "date": date,
            "data_table_name": table_name,
            "biz_name_lvl2": biz_name_lvl2
        }

        # 调用外部webhook
        response = requests.post(
            webhook_url,
            json=webhook_data,
            headers={'Content-Type': 'application/json'},
            timeout=30  # 30秒超时
        )

        if response.status_code == 200:
            try:
                webhook_result = response.json()
                return jsonify({
                    "code": 0,
                    "msg": f"RPA重跑请求已提交成功！品牌: {brand}, 平台: {platform}, 日期: {date}",
                    "webhook_response": webhook_result
                })
            except:
                # 如果响应不是JSON格式，直接返回成功
                return jsonify({
                    "code": 0,
                    "msg": f"RPA重跑请求已提交成功！品牌: {brand}, 平台: {platform}, 日期: {date}",
                    "webhook_response": response.text
                })
        else:
            return jsonify({
                "code": 1,
                "msg": f"RPA重跑失败: webhook调用失败，状态码: {response.status_code}，响应: {response.text}"
            }), 500

    except requests.exceptions.Timeout:
        return jsonify({"code": 1, "msg": "RPA重跑失败: webhook调用超时"}), 500
    except requests.exceptions.RequestException as e:
        return jsonify({"code": 1, "msg": f"RPA重跑失败: 网络请求异常 - {str(e)}"}), 500
    except Exception as e:
        return jsonify({"code": 1, "msg": f"RPA重跑失败: {str(e)}"}), 500


@app.route("/api/refresh_dw", methods=["POST"])
def refresh_dw():
    """数仓刷数接口"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({"code": 1, "msg": "请求数据不能为空"}), 400
        brand = data.get('brand', '')
        platform = data.get('platform', '')
        date = data.get('date', '')
        dw_table_name = data.get('dw_table_name', '')

        print(f"数仓刷数请求: 品牌={brand}, 平台={platform}, 日期={date}, 数仓表名={dw_table_name}")
        return jsonify({
            "code": 0,
            "msg": f"数仓刷数请求已提交成功！品牌: {brand}, 平台: {platform}, 日期: {date}"
        })

    except Exception as e:
        return jsonify({"code": 1, "msg": f"数仓刷数失败: {str(e)}"}), 500


@app.route("/api/business_options", methods=["GET"])
def get_business_options():
    """获取业务选项：二级业务类型和周期维度"""
    try:
        biz_name_lvl1 = request.args.get("biz_name_lvl1")

        if not biz_name_lvl1:
            return jsonify({"code": 1, "msg": "biz_name_lvl1参数不能为空"}), 400

        conn = get_connection()
        with conn.cursor() as cursor:
            # 获取二级业务选项
            cursor.execute("""
                SELECT DISTINCT biz_name_lvl2
                FROM rpa_data_monitor
                WHERE biz_name_lvl1 = %s AND biz_name_lvl2 IS NOT NULL
                ORDER BY biz_name_lvl2
            """, [biz_name_lvl1])
            biz_lvl2_rows = cursor.fetchall()
            biz_lvl2_options = [row['biz_name_lvl2'] for row in biz_lvl2_rows if row['biz_name_lvl2']]

            # 获取周期维度选项
            cursor.execute("""
                SELECT DISTINCT period_type
                FROM rpa_data_monitor
                WHERE biz_name_lvl1 = %s AND period_type IS NOT NULL
                ORDER BY period_type
            """, [biz_name_lvl1])
            period_type_rows = cursor.fetchall()
            period_type_options = [row['period_type'] for row in period_type_rows if row['period_type']]

        result = {
            "biz_lvl2_options": biz_lvl2_options,
            "period_type_options": period_type_options
        }

        return jsonify({"code": 0, "msg": "success", "data": result})
    except Exception as e:
        return jsonify({"code": 1, "msg": str(e)}), 500
    finally:
        try:
            conn.close()
        except Exception:
            pass


@app.route("/api/brand_trend", methods=["GET"])
def get_brand_trend():
    """获取品牌数据量走势"""
    try:
        brand = request.args.get("brand")
        platform = request.args.get("platform")
        start_date = request.args.get("start_date")
        end_date = request.args.get("end_date")
        biz_name_lvl1 = request.args.get("biz_name_lvl1")
        biz_name_lvl2 = request.args.get("biz_name_lvl2")
        period_type = request.args.get("period_type")
        data_type = request.args.get("data_type")

        if not brand:
            return jsonify({"code": 1, "msg": "品牌参数不能为空"}), 400

        params = []
        where_clauses = []

        # 解析品牌和平台（格式：品牌名(平台名)）
        if "(" in brand and brand.endswith(")"):
            brand_name = brand.split("(")[0]
            platform_name = brand.split("(")[1][:-1]
            where_clauses.append("brand = %s")
            params.append(brand_name)
            where_clauses.append("platform = %s")
            params.append(platform_name)
        else:
            where_clauses.append("brand = %s")
            params.append(brand)
            if platform:
                where_clauses.append("platform = %s")
                params.append(platform)

        if biz_name_lvl1:
            where_clauses.append("biz_name_lvl1 = %s")
            params.append(biz_name_lvl1)

        if biz_name_lvl2:
            where_clauses.append("biz_name_lvl2 = %s")
            params.append(biz_name_lvl2)

        if period_type:
            where_clauses.append("period_type = %s")
            params.append(period_type)

        if data_type:
            where_clauses.append("data_type = %s")
            params.append(data_type)

        if start_date:
            # 根据日期格式判断是日维度还是月维度
            if len(start_date) == 7 and start_date[4] == '-':
                # YYYY-MM 格式（月维度）
                where_clauses.append("date >= %s")
                params.append(start_date)
            elif len(start_date) == 10 and start_date[4] == '-':
                # YYYY-MM-DD 格式（日维度）
                where_clauses.append("date >= %s")
                params.append(start_date)
            else:
                # 其他格式直接使用
                where_clauses.append("date >= %s")
                params.append(start_date)

        if end_date:
            # 根据日期格式判断是日维度还是月维度
            if len(end_date) == 7 and end_date[4] == '-':
                # YYYY-MM 格式（月维度）
                where_clauses.append("date <= %s")
                params.append(end_date)
            elif len(end_date) == 10 and end_date[4] == '-':
                # YYYY-MM-DD 格式（日维度）
                where_clauses.append("date <= %s")
                params.append(end_date)
            else:
                # 其他格式直接使用
                where_clauses.append("date <= %s")
                params.append(end_date)

        where_sql = f"WHERE {' AND '.join(where_clauses)}" if where_clauses else ""

        # 根据period_type决定日期格式和分组
        if period_type == '月':
            # 月维度：直接使用date字段（已经是YYYY-MM格式）
            sql = (
                "SELECT date, "
                "SUM(db_data_count) as total_db_count, "
                "SUM(dw_data_count) as total_dw_count "
                "FROM rpa_data_monitor "
                f"{where_sql} "
                "GROUP BY date "
                "ORDER BY date"
            )
        else:
            # 日维度：使用date字段（已经是YYYY-MM-DD格式）
            sql = (
                "SELECT date, "
                "SUM(db_data_count) as total_db_count, "
                "SUM(dw_data_count) as total_dw_count "
                "FROM rpa_data_monitor "
                f"{where_sql} "
                "GROUP BY date "
                "ORDER BY date"
            )

        conn = get_connection()
        with conn.cursor() as cursor:
            cursor.execute(sql, params)
            rows = cursor.fetchall()

            # 构建数据字典，方便查找
            data_dict = {}
            for row in rows:
                data_dict[row['date']] = {
                    'db_count': row['total_db_count'] or 0,
                    'dw_count': row['total_dw_count'] or 0
                }

            # 生成完整的日期范围
            from datetime import datetime, timedelta

            # 如果没有指定日期范围，使用默认范围
            if not start_date or not end_date:
                if period_type == '月':
                    # 月维度：2024年1月至T-1月
                    current_date = datetime.now()
                    last_month = current_date.replace(day=1) - timedelta(days=1)
                    end_date = last_month.strftime('%Y-%m')
                    start_date = '2024-01'
                else:
                    # 日维度：T-1日往前推15天
                    yesterday = datetime.now() - timedelta(days=1)
                    end_date = yesterday.strftime('%Y-%m-%d')
                    start_date = (yesterday - timedelta(days=14)).strftime('%Y-%m-%d')
                print(f"使用默认日期范围: {start_date} 到 {end_date}")

            dates = []
            db_counts = []
            dw_counts = []

            if period_type == '月':
                # 月维度：生成YYYY-MM格式的日期列表
                if len(start_date) == 10:  # YYYY-MM-DD格式转换为YYYY-MM
                    start_year, start_month = int(start_date[:4]), int(start_date[5:7])
                elif len(start_date) == 7:  # YYYY-MM格式
                    start_year, start_month = int(start_date[:4]), int(start_date[5:7])
                else:
                    raise ValueError(f"Invalid start_date format: {start_date}")

                if len(end_date) == 10:  # YYYY-MM-DD格式转换为YYYY-MM
                    end_year, end_month = int(end_date[:4]), int(end_date[5:7])
                elif len(end_date) == 7:  # YYYY-MM格式
                    end_year, end_month = int(end_date[:4]), int(end_date[5:7])
                else:
                    raise ValueError(f"Invalid end_date format: {end_date}")

                current_year, current_month = start_year, start_month
                while (current_year < end_year) or (current_year == end_year and current_month <= end_month):
                    date_str = f"{current_year:04d}-{current_month:02d}"
                    dates.append(date_str)

                    # 如果该月份有数据，使用实际数据；否则使用0
                    if date_str in data_dict:
                        db_counts.append(data_dict[date_str]['db_count'])
                        dw_counts.append(data_dict[date_str]['dw_count'])
                    else:
                        db_counts.append(0)
                        dw_counts.append(0)

                    current_month += 1
                    if current_month > 12:
                        current_month = 1
                        current_year += 1
            else:
                # 日维度：生成YYYY-MM-DD格式的日期列表
                start_dt = datetime.strptime(start_date, '%Y-%m-%d')
                end_dt = datetime.strptime(end_date, '%Y-%m-%d')

                current_dt = start_dt
                while current_dt <= end_dt:
                    date_str = current_dt.strftime('%Y-%m-%d')
                    dates.append(date_str)

                    # 如果该日期有数据，使用实际数据；否则使用0
                    if date_str in data_dict:
                        db_counts.append(data_dict[date_str]['db_count'])
                        dw_counts.append(data_dict[date_str]['dw_count'])
                    else:
                        db_counts.append(0)
                        dw_counts.append(0)

                    current_dt += timedelta(days=1)

            trend_data = {
                "dates": dates,
                "db_counts": db_counts,
                "dw_counts": dw_counts,
                "brand": brand
            }

        return jsonify({"code": 0, "msg": "success", "data": trend_data})

    except Exception as e:
        return jsonify({"code": 1, "msg": str(e)}), 500
    finally:
        try:
            conn.close()
        except Exception:
            pass


if __name__ == "__main__":
    app.run(host="0.0.0.0", port=5001, debug=True)



import os
import pymysql


def get_connection():
    host = os.getenv("DB_HOST", "rm-uf6d863v517727p6mdo.mysql.rds.aliyuncs.com")
    user = os.getenv("DB_USER", "heyin_data")
    password = os.getenv("DB_PASSWORD", "oa28eUf2hefy2r")
    database = os.getenv("DB_NAME", "heyin_rpa_status")

    return pymysql.connect(
        host=host,
        user=user,
        password=password,
        database=database,
        charset="utf8mb4",
        cursorclass=pymysql.cursors.DictCursor,
        autocommit=True,
    )



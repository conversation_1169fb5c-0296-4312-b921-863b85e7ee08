-- rpa监控表
CREATE TABLE `rpa_data_monitor` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `biz_name_lvl1` varchar(100) NOT NULL COMMENT '业务一级分类',
  `biz_name_lvl2` varchar(100) NOT NULL COMMENT '业务二级分类',
  `date` date NOT NULL COMMENT '数据日期',
  `platform` varchar(50) NOT NULL COMMENT '平台名称',
  `brand` varchar(50) NOT NULL COMMENT '品牌名称',
  `data_table_name` varchar(100) DEFAULT NULL COMMENT '数据表名',
  `db_exists` tinyint(1) NOT NULL DEFAULT '0' COMMENT '数据库是否存在',
  `dw_table_name` varchar(100) DEFAULT NULL COMMENT '数仓表名',
  `dw_exists` tinyint(1) NOT NULL DEFAULT '0' COMMENT '数仓是否存在',
  `dw_data_count` int NOT NULL DEFAULT '0' COMMENT '数仓数据条数',
  `db_data_count` int NOT NULL DEFAULT '0' COMMENT '数据库数据条数',
  `dw_refresh_time` datetime DEFAULT NULL COMMENT '数仓刷数时间',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=6899 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='数据监控表'

-- 系统用户表
CREATE TABLE sys_user (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '用户ID',
    username VARCHAR(50) NOT NULL UNIQUE COMMENT '用户名',
    password VARCHAR(255) NOT NULL COMMENT '密码（加密存储）',
		role TINYINT NOT NULL DEFAULT 1 COMMENT '权限',
		created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='系统用户表';


-- RPA任务表
CREATE TABLE rpa_task (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    rpa_owner_name VARCHAR(50) NOT NULL COMMENT 'RPA负责人名称',
		data_table_name VARCHAR(100) DEFAULT NULL COMMENT '数据表名',
    rpa_task_desc VARCHAR(255) DEFAULT NULL COMMENT 'RPA任务描述',
    yingdao_api_name VARCHAR(100) NOT NULL COMMENT '影刀接口名称',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='RPA任务表';

-- 数仓任务表
CREATE TABLE dw_task (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
		dw_owner_name VARCHAR(50) NOT NULL COMMENT '数仓负责人名称',
		dw_table_name VARCHAR(100) DEFAULT NULL COMMENT '数仓表名',
		dw_task_desc VARCHAR(255) DEFAULT NULL COMMENT '数仓任务描述',
    dw_api_name VARCHAR(100) NOT NULL COMMENT '数仓接口名称',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='数仓任务表';